#!/usr/bin/env node

/**
 * Demo script để test JSON peaks format
 * Tạo file JSON tương thích với wavesurfer.js
 */

const fs = require('fs');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Copy functions từ index.js
async function generateAudioWaveform(inputFile, outputFile, options = {}) {
    console.log(`[INFO] Generating waveform using audiowaveform...`);
    
    const {
        width = 1800,
        height = 280,
        zoom = 256,
        format = 'json'
    } = options;

    try {
        const command = [
            'audiowaveform',
            '-i', `"${inputFile}"`,
            '-o', `"${outputFile}"`,
            '--width', width.toString(),
            '--height', height.toString(),
            '--zoom', zoom.toString()
        ];

        if (format === 'json') {
            command.push('--output-format', 'json');
        } else if (format === 'dat') {
            command.push('--output-format', 'dat');
        }

        const commandString = command.join(' ');
        console.log(`[INFO] Executing: ${commandString}`);

        const { stdout, stderr } = await execAsync(commandString);
        
        if (stderr) {
            console.log(`[WARN] audiowaveform stderr: ${stderr}`);
        }

        console.log(`[SUCCESS] Waveform generated successfully: ${outputFile}`);
        return outputFile;

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform: ${error.message}`);
        throw error;
    }
}

async function generateWaveformPeaksFromAudiowaveform(inputFile, outputFile, numPeaks = 32, outputFormat = 'json') {
    console.log(`[INFO] Generating waveform peaks using audiowaveform (format: ${outputFormat})...`);
    
    try {
        const tempJsonFile = `/tmp/temp_waveform_${Date.now()}.json`;
        
        await generateAudioWaveform(inputFile, tempJsonFile, {
            width: numPeaks,
            format: 'json'
        });

        const jsonData = JSON.parse(fs.readFileSync(tempJsonFile, 'utf8'));
        
        if (!jsonData.data || !Array.isArray(jsonData.data)) {
            throw new Error('Invalid waveform JSON format');
        }

        console.log(`[DEBUG] Audiowaveform JSON data length: ${jsonData.data.length}`);
        
        const waveformData = jsonData.data;
        const mergedPeaks = [];
        
        if (waveformData.length === numPeaks * 2) {
            console.log(`[DEBUG] Using min/max pairs format`);
            for (let i = 0; i < waveformData.length; i += 2) {
                const min = waveformData[i] || 0;
                const max = waveformData[i + 1] || 0;
                
                const normalizedMin = min / (Math.pow(2, jsonData.bits - 1));
                const normalizedMax = max / (Math.pow(2, jsonData.bits - 1));
                
                mergedPeaks.push(normalizedMin);
                mergedPeaks.push(normalizedMax);
            }
        } else {
            console.log(`[DEBUG] Using alternative format, creating min/max from single values`);
            const samplesPerPeak = Math.floor(waveformData.length / numPeaks);
            
            for (let i = 0; i < numPeaks; i++) {
                const startIdx = i * samplesPerPeak;
                const endIdx = Math.min(startIdx + samplesPerPeak, waveformData.length);
                
                let min = Infinity;
                let max = -Infinity;
                
                for (let j = startIdx; j < endIdx; j++) {
                    const value = waveformData[j] / (Math.pow(2, jsonData.bits - 1));
                    min = Math.min(min, value);
                    max = Math.max(max, value);
                }
                
                mergedPeaks.push(min === Infinity ? 0 : min);
                mergedPeaks.push(max === -Infinity ? 0 : max);
            }
        }

        const formattedPeaks = mergedPeaks.map(peak => {
            return Math.round(peak * Math.pow(10, 8)) / Math.pow(10, 8);
        });

        console.log(`[DEBUG] Generated ${formattedPeaks.length} peak values (${formattedPeaks.length/2} peaks)`);

        if (outputFormat === 'json') {
            const wavesurferJson = {
                version: 2,
                channels: jsonData.channels || 1,
                sample_rate: jsonData.sample_rate || 44100,
                samples_per_pixel: jsonData.samples_per_pixel || 512,
                bits: 8,
                length: formattedPeaks.length,
                data: formattedPeaks
            };
            
            fs.writeFileSync(outputFile, JSON.stringify(wavesurferJson, null, 2));
            console.log(`[SUCCESS] JSON waveform peaks generated: ${outputFile}`);
        } else {
            const peaksString = formattedPeaks.map(peak => peak.toString()).join(' ');
            fs.writeFileSync(outputFile, peaksString);
            console.log(`[SUCCESS] Text waveform peaks generated: ${outputFile}`);
        }

        if (fs.existsSync(tempJsonFile)) {
            fs.unlinkSync(tempJsonFile);
        }

        return { outputFile, formattedPeaks };

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform peaks: ${error.message}`);
        throw error;
    }
}

async function createTestWavFile() {
    console.log('[DEMO] Creating test WAV file...');
    
    const testAudioFile = '/tmp/demo_audio.wav';
    const sampleRate = 44100;
    const duration = 3;
    const samples = sampleRate * duration;
    
    // Tạo WAV header
    const wavHeader = Buffer.alloc(44);
    wavHeader.write('RIFF', 0);
    wavHeader.writeUInt32LE(36 + samples * 2, 4);
    wavHeader.write('WAVE', 8);
    wavHeader.write('fmt ', 12);
    wavHeader.writeUInt32LE(16, 16);
    wavHeader.writeUInt16LE(1, 20);
    wavHeader.writeUInt16LE(1, 22);
    wavHeader.writeUInt32LE(sampleRate, 24);
    wavHeader.writeUInt32LE(sampleRate * 2, 28);
    wavHeader.writeUInt16LE(2, 32);
    wavHeader.writeUInt16LE(16, 34);
    wavHeader.write('data', 36);
    wavHeader.writeUInt32LE(samples * 2, 40);
    
    // Tạo complex audio data
    const audioData = Buffer.alloc(samples * 2);
    for (let i = 0; i < samples; i++) {
        const t = i / sampleRate;
        const freq1 = 440 + 100 * Math.sin(2 * Math.PI * 0.5 * t);
        const freq2 = 880 * Math.cos(2 * Math.PI * 0.3 * t);
        const amplitude = 0.4 + 0.6 * Math.sin(2 * Math.PI * 0.2 * t);
        
        const sample = amplitude * (
            0.7 * Math.sin(2 * Math.PI * freq1 * t) +
            0.3 * Math.sin(2 * Math.PI * freq2 * t)
        ) * 32767;
        
        audioData.writeInt16LE(Math.round(sample), i * 2);
    }
    
    fs.writeFileSync(testAudioFile, Buffer.concat([wavHeader, audioData]));
    console.log(`[SUCCESS] Test WAV file created: ${testAudioFile}`);
    
    return testAudioFile;
}

async function demoJsonPeaks() {
    console.log('=== Demo: JSON Peaks Generation ===\n');
    
    try {
        // Tạo test file
        const testFile = await createTestWavFile();
        
        // Tạo JSON peaks
        console.log('\n[DEMO] Generating JSON peaks...');
        const jsonFile = '/tmp/demo_peaks.json';
        const result = await generateWaveformPeaksFromAudiowaveform(testFile, jsonFile, 32, 'json');
        
        // Đọc và hiển thị JSON
        const jsonData = JSON.parse(fs.readFileSync(jsonFile, 'utf8'));
        console.log('\n[DEMO] Generated JSON structure:');
        console.log(JSON.stringify(jsonData, null, 2));
        
        // Tạo text peaks để so sánh
        console.log('\n[DEMO] Generating text peaks for comparison...');
        const textFile = '/tmp/demo_peaks.txt';
        await generateWaveformPeaksFromAudiowaveform(testFile, textFile, 32, 'text');
        
        const textData = fs.readFileSync(textFile, 'utf8');
        console.log('\n[DEMO] Text format:');
        console.log(textData.substring(0, 200) + '...');
        
        // Frontend usage example
        console.log('\n[DEMO] Frontend usage example:');
        console.log('```javascript');
        console.log('// Load JSON peaks');
        console.log('fetch("/path/to/file_peaks.json")');
        console.log('  .then(response => response.json())');
        console.log('  .then(peaksData => {');
        console.log('    // peaksData.data contains the peaks array');
        console.log('    wavesurfer.backend.setMergedPeaks(peaksData.data);');
        console.log('  });');
        console.log('```');
        
        // Cleanup
        [testFile, jsonFile, textFile].forEach(file => {
            if (fs.existsSync(file)) {
                fs.unlinkSync(file);
            }
        });
        
        console.log('\n=== Demo Complete ===');
        
    } catch (error) {
        console.error(`[ERROR] Demo failed: ${error.message}`);
    }
}

if (require.main === module) {
    demoJsonPeaks();
}

module.exports = { demoJsonPeaks };
