#!/usr/bin/env node

/**
 * Test script để kiểm tra audiowaveform integration
 * Chạy: node test-audiowaveform.js
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Test functions (copy từ index.js)
async function generateAudioWaveform(inputFile, outputFile, options = {}) {
    console.log(`[INFO] Generating waveform using audiowaveform...`);
    
    const {
        width = 1800,
        height = 280,
        zoom = 256,
        format = 'json'
    } = options;

    try {
        const command = [
            'audiowaveform',
            '-i', `"${inputFile}"`,
            '-o', `"${outputFile}"`,
            '--width', width.toString(),
            '--height', height.toString(),
            '--zoom', zoom.toString()
        ];

        if (format === 'json') {
            command.push('--output-format', 'json');
        } else if (format === 'dat') {
            command.push('--output-format', 'dat');
        }

        const commandString = command.join(' ');
        console.log(`[INFO] Executing: ${commandString}`);

        const { stdout, stderr } = await execAsync(commandString);
        
        if (stderr) {
            console.log(`[WARN] audiowaveform stderr: ${stderr}`);
        }
        
        if (stdout) {
            console.log(`[INFO] audiowaveform stdout: ${stdout}`);
        }

        console.log(`[SUCCESS] Waveform generated successfully: ${outputFile}`);
        return outputFile;

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform: ${error.message}`);
        throw error;
    }
}

async function generateWaveformPeaksFromAudiowaveform(inputFile, outputFile, numPeaks = 32) {
    console.log(`[INFO] Generating waveform peaks using audiowaveform...`);
    
    try {
        const tempJsonFile = `/tmp/temp_waveform_${Date.now()}.json`;
        
        await generateAudioWaveform(inputFile, tempJsonFile, {
            width: numPeaks * 2,
            format: 'json'
        });

        const jsonData = JSON.parse(fs.readFileSync(tempJsonFile, 'utf8'));
        
        if (!jsonData.data || !Array.isArray(jsonData.data)) {
            throw new Error('Invalid waveform JSON format');
        }

        const waveformData = jsonData.data;
        const mergedPeaks = [];
        
        for (let i = 0; i < waveformData.length; i += 2) {
            const min = (waveformData[i] || 0) / 32768.0;
            const max = (waveformData[i + 1] || 0) / 32768.0;
            
            mergedPeaks.push(Math.min(min, 0));
            mergedPeaks.push(Math.max(max, 0));
        }

        const peaksString = mergedPeaks.map(peak => {
            const formattedPeak = Math.round(peak * Math.pow(10, 8)) / Math.pow(10, 8);
            return formattedPeak.toString();
        }).join(' ');

        fs.writeFileSync(outputFile, peaksString);

        if (fs.existsSync(tempJsonFile)) {
            fs.unlinkSync(tempJsonFile);
        }

        console.log(`[SUCCESS] Waveform peaks generated successfully using audiowaveform`);
        return outputFile;

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform peaks: ${error.message}`);
        throw error;
    }
}

// Test function
async function testAudiowaveform() {
    console.log('=== Testing Audiowaveform Integration ===\n');

    try {
        // Kiểm tra xem audiowaveform có available không
        console.log('[TEST 1] Checking audiowaveform availability...');
        const { stdout } = await execAsync('audiowaveform --version');
        console.log(`[SUCCESS] audiowaveform version: ${stdout.trim()}\n`);

        // Tạo test audio file (sine wave)
        console.log('[TEST 2] Creating test audio file...');
        const testAudioFile = '/tmp/test_audio.wav';
        await execAsync(`ffmpeg -f lavfi -i "sine=frequency=440:duration=5" -y "${testAudioFile}"`);
        console.log(`[SUCCESS] Test audio file created: ${testAudioFile}\n`);

        // Test generateAudioWaveform
        console.log('[TEST 3] Testing generateAudioWaveform...');
        const waveformJsonFile = '/tmp/test_waveform.json';
        await generateAudioWaveform(testAudioFile, waveformJsonFile);
        
        if (fs.existsSync(waveformJsonFile)) {
            const jsonData = JSON.parse(fs.readFileSync(waveformJsonFile, 'utf8'));
            console.log(`[SUCCESS] Waveform JSON generated with ${jsonData.data.length} data points\n`);
        }

        // Test generateWaveformPeaksFromAudiowaveform
        console.log('[TEST 4] Testing generateWaveformPeaksFromAudiowaveform...');
        const peaksFile = '/tmp/test_peaks.txt';
        await generateWaveformPeaksFromAudiowaveform(testAudioFile, peaksFile, 32);
        
        if (fs.existsSync(peaksFile)) {
            const peaksData = fs.readFileSync(peaksFile, 'utf8');
            const peaksArray = peaksData.split(' ');
            console.log(`[SUCCESS] Peaks file generated with ${peaksArray.length} peaks\n`);
        }

        // Cleanup
        console.log('[CLEANUP] Removing test files...');
        [testAudioFile, waveformJsonFile, peaksFile].forEach(file => {
            if (fs.existsSync(file)) {
                fs.unlinkSync(file);
                console.log(`[CLEANUP] Removed: ${file}`);
            }
        });

        console.log('\n=== All tests passed! Audiowaveform integration is working correctly ===');

    } catch (error) {
        console.error(`[ERROR] Test failed: ${error.message}`);
        process.exit(1);
    }
}

// Chạy test nếu script được gọi trực tiếp
if (require.main === module) {
    testAudiowaveform();
}

module.exports = {
    generateAudioWaveform,
    generateWaveformPeaksFromAudiowaveform,
    testAudiowaveform
};
