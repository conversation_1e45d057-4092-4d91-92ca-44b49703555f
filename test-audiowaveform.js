#!/usr/bin/env node

/**
 * Test script for audiowaveform integration
 * Run: node test-audiowaveform.js
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Test functions (copied from index.js)
async function generateAudioWaveform(inputFile, outputFile, options = {}) {
    console.log(`[INFO] Generating waveform using audiowaveform...`);
    
    const {
        width = 1800,
        height = 280,
        zoom = 256,
        format = 'json'
    } = options;

    try {
        const command = [
            'audiowaveform',
            '-i', `"${inputFile}"`,
            '-o', `"${outputFile}"`,
            '--width', width.toString(),
            '--height', height.toString(),
            '--zoom', zoom.toString()
        ];

        if (format === 'json') {
            command.push('--output-format', 'json');
        } else if (format === 'dat') {
            command.push('--output-format', 'dat');
        }

        const commandString = command.join(' ');
        console.log(`[INFO] Executing: ${commandString}`);

        const { stdout, stderr } = await execAsync(commandString);
        
        if (stderr) {
            console.log(`[WARN] audiowaveform stderr: ${stderr}`);
        }
        
        if (stdout) {
            console.log(`[INFO] audiowaveform stdout: ${stdout}`);
        }

        console.log(`[SUCCESS] Waveform generated successfully: ${outputFile}`);
        return outputFile;

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform: ${error.message}`);
        throw error;
    }
}

async function generateWaveformPeaksFromAudiowaveform(inputFile, outputFile, numPeaks = 32) {
    console.log(`[INFO] Generating waveform peaks using audiowaveform...`);

    try {
        const tempJsonFile = `/tmp/temp_waveform_${Date.now()}.json`;

        await generateAudioWaveform(inputFile, tempJsonFile, {
            width: numPeaks,
            format: 'json'
        });

        const jsonData = JSON.parse(fs.readFileSync(tempJsonFile, 'utf8'));

        if (!jsonData.data || !Array.isArray(jsonData.data)) {
            throw new Error('Invalid waveform JSON format');
        }

        console.log(`[DEBUG] Audiowaveform JSON data length: ${jsonData.data.length}`);

        const waveformData = jsonData.data;
        const mergedPeaks = [];

        // Xử lý linh hoạt tùy theo format của audiowaveform
        if (waveformData.length === numPeaks * 2) {
            // Format: [min1, max1, min2, max2, ...]
            for (let i = 0; i < waveformData.length; i += 2) {
                const min = waveformData[i] || 0;
                const max = waveformData[i + 1] || 0;

                const normalizedMin = min / (Math.pow(2, jsonData.bits - 1));
                const normalizedMax = max / (Math.pow(2, jsonData.bits - 1));

                mergedPeaks.push(normalizedMin);
                mergedPeaks.push(normalizedMax);
            }
        } else {
            // Format khác - tạo min/max từ single values
            const samplesPerPeak = Math.floor(waveformData.length / numPeaks);

            for (let i = 0; i < numPeaks; i++) {
                const startIdx = i * samplesPerPeak;
                const endIdx = Math.min(startIdx + samplesPerPeak, waveformData.length);

                let min = Infinity;
                let max = -Infinity;

                for (let j = startIdx; j < endIdx; j++) {
                    const value = waveformData[j] / (Math.pow(2, jsonData.bits - 1));
                    min = Math.min(min, value);
                    max = Math.max(max, value);
                }

                mergedPeaks.push(min === Infinity ? 0 : min);
                mergedPeaks.push(max === -Infinity ? 0 : max);
            }
        }

        const peaksString = mergedPeaks.map(peak => {
            const formattedPeak = Math.round(peak * Math.pow(10, 8)) / Math.pow(10, 8);
            return formattedPeak.toString();
        }).join(' ');

        fs.writeFileSync(outputFile, peaksString);

        if (fs.existsSync(tempJsonFile)) {
            fs.unlinkSync(tempJsonFile);
        }

        console.log(`[SUCCESS] Waveform peaks generated successfully using audiowaveform`);
        return outputFile;

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform peaks: ${error.message}`);
        throw error;
    }
}

// Test function
async function testAudiowaveform() {
    console.log('=== Testing Audiowaveform Integration ===\n');

    try {
        // Kiểm tra xem audiowaveform có available không
        console.log('[TEST 1] Checking audiowaveform availability...');
        const { stdout } = await execAsync('audiowaveform --version');
        console.log(`[SUCCESS] audiowaveform version: ${stdout.trim()}\n`);

        // Tạo test audio file bằng audiowaveform (sử dụng file mẫu)
        console.log('[TEST 2] Creating test audio file...');
        const testAudioFile = '/tmp/test_audio.wav';
        // Tạo một file WAV đơn giản (sine wave 440Hz, 5 giây)
        const sampleRate = 44100;
        const duration = 5;
        const frequency = 440;
        const samples = sampleRate * duration;

        // Tạo WAV header
        const wavHeader = Buffer.alloc(44);
        wavHeader.write('RIFF', 0);
        wavHeader.writeUInt32LE(36 + samples * 2, 4);
        wavHeader.write('WAVE', 8);
        wavHeader.write('fmt ', 12);
        wavHeader.writeUInt32LE(16, 16);
        wavHeader.writeUInt16LE(1, 20);
        wavHeader.writeUInt16LE(1, 22);
        wavHeader.writeUInt32LE(sampleRate, 24);
        wavHeader.writeUInt32LE(sampleRate * 2, 28);
        wavHeader.writeUInt16LE(2, 32);
        wavHeader.writeUInt16LE(16, 34);
        wavHeader.write('data', 36);
        wavHeader.writeUInt32LE(samples * 2, 40);

        // Tạo audio data (sine wave)
        const audioData = Buffer.alloc(samples * 2);
        for (let i = 0; i < samples; i++) {
            const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 32767;
            audioData.writeInt16LE(sample, i * 2);
        }

        // Ghi file WAV
        fs.writeFileSync(testAudioFile, Buffer.concat([wavHeader, audioData]));
        console.log(`[SUCCESS] Test audio file created: ${testAudioFile}\n`);

        // Test generateAudioWaveform
        console.log('[TEST 3] Testing generateAudioWaveform...');
        const waveformJsonFile = '/tmp/test_waveform.json';
        await generateAudioWaveform(testAudioFile, waveformJsonFile);
        
        if (fs.existsSync(waveformJsonFile)) {
            const jsonData = JSON.parse(fs.readFileSync(waveformJsonFile, 'utf8'));
            console.log(`[SUCCESS] Waveform JSON generated with ${jsonData.data.length} data points\n`);
        }

        // Test generateWaveformPeaksFromAudiowaveform - JSON format
        console.log('[TEST 4] Testing generateWaveformPeaksFromAudiowaveform (JSON)...');
        const peaksJsonFile = '/tmp/test_peaks.json';
        await generateWaveformPeaksFromAudiowaveform(testAudioFile, peaksJsonFile, 32, 'json');

        if (fs.existsSync(peaksJsonFile)) {
            const jsonData = JSON.parse(fs.readFileSync(peaksJsonFile, 'utf8'));
            console.log(`[SUCCESS] JSON peaks file generated:`);
            console.log(`  - version: ${jsonData.version}`);
            console.log(`  - channels: ${jsonData.channels}`);
            console.log(`  - sample_rate: ${jsonData.sample_rate}`);
            console.log(`  - data length: ${jsonData.data.length}`);
            console.log(`  - first 10 values: ${jsonData.data.slice(0, 10)}\n`);
        }

        // Test text format cũng
        console.log('[TEST 5] Testing generateWaveformPeaksFromAudiowaveform (text)...');
        const peaksTextFile = '/tmp/test_peaks.txt';
        await generateWaveformPeaksFromAudiowaveform(testAudioFile, peaksTextFile, 32, 'text');

        if (fs.existsSync(peaksTextFile)) {
            const peaksData = fs.readFileSync(peaksTextFile, 'utf8');
            const peaksArray = peaksData.split(' ');
            console.log(`[SUCCESS] Text peaks file generated with ${peaksArray.length} peaks\n`);
        }

        // Cleanup
        console.log('[CLEANUP] Removing test files...');
        [testAudioFile, waveformJsonFile, peaksJsonFile, peaksTextFile].forEach(file => {
            if (fs.existsSync(file)) {
                fs.unlinkSync(file);
                console.log(`[CLEANUP] Removed: ${file}`);
            }
        });

        console.log('\n=== All tests passed! Audiowaveform integration is working correctly ===');

    } catch (error) {
        console.error(`[ERROR] Test failed: ${error.message}`);
        process.exit(1);
    }
}

// Chạy test nếu script được gọi trực tiếp
if (require.main === module) {
    testAudiowaveform();
}

module.exports = {
    generateAudioWaveform,
    generateWaveformPeaksFromAudiowaveform,
    testAudiowaveform
};
