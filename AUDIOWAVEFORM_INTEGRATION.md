# Audiowaveform Integration

## Tổng quan

Dự án đã được tích hợp với [audiowaveform](https://github.com/bbc/audiowaveform) - một công cụ mạnh mẽ để tạo waveform data từ audio files. Audiowaveform được phát triển bởi BBC và cung cấp hiệu suất tốt hơn so với việc sử dụng FFmpeg để tạo waveform.

## Tính năng

- **Hiệu suất cao**: audiowaveform được tối ưu hóa cho việc tạo waveform data
- **Hỗ trợ nhiều format**: MP3, WAV, FLAC, OGG, M4A, AAC
- **Fallback mechanism**: Tự động chuyển về FFmpeg nếu audiowaveform thất bại
- **C<PERSON>u hình linh hoạt**: C<PERSON> thể bật/tắt qua environment variable

## Cấu hình

### Environment Variables

- `USE_AUDIOWAVEFORM`: Set thành `true` để sử dụng audiowaveform, `false` để sử dụng FFmpeg (default: `false`)

### Ví dụ cấu hình <PERSON>

```json
{
  "Environment": {
    "Variables": {
      "USE_AUDIOWAVEFORM": "true",
      "OUTPUT_BUCKET": "your-output-bucket",
      "MEDIA_CONVERT_ROLE_ARN": "your-role-arn",
      "MEDIA_CONVERT_ENDPOINT": "your-endpoint",
      "RDS_PARAM_NAME": "your-rds-param"
    }
  }
}
```

## Cách hoạt động

### Khi USE_AUDIOWAVEFORM = true

1. Hệ thống sẽ thử sử dụng audiowaveform để tạo waveform data
2. Nếu audiowaveform thất bại, tự động fallback về FFmpeg
3. Waveform data được tạo ở format tương thích với wavesurfer.js

### Khi USE_AUDIOWAVEFORM = false

1. Hệ thống sử dụng FFmpeg như trước đây
2. Không có thay đổi về behavior

## Functions mới

### generateAudioWaveform()

Tạo waveform data sử dụng audiowaveform binary.

```javascript
await generateAudioWaveform(inputFile, outputFile, {
    width: 1800,
    height: 280,
    zoom: 256,
    format: 'json'
});
```

### generateWaveformPeaksFromAudiowaveform()

Tạo wavesurfer-compatible peaks data từ audiowaveform.

```javascript
await generateWaveformPeaksFromAudiowaveform(inputFile, outputFile, numPeaks);
```

## Build Process

Audiowaveform được build trong Docker multi-stage build:

1. **audiowaveform-builder stage**: Build audiowaveform từ source với tất cả dependencies
2. **Final stage**: Copy audiowaveform binary vào Lambda runtime

### Dependencies được build

- libid3tag
- libmad  
- FLAC
- libogg
- libvorbis
- Opus
- libgd
- libsndfile
- Boost libraries

## Troubleshooting

### Audiowaveform không hoạt động

1. Kiểm tra logs để xem error message
2. Hệ thống sẽ tự động fallback về FFmpeg
3. Đảm bảo file input có format được hỗ trợ

### Performance Issues

1. Audiowaveform thường nhanh hơn FFmpeg
2. Nếu gặp vấn đề, có thể tạm thời set `USE_AUDIOWAVEFORM=false`

## Logs

Hệ thống sẽ log rõ ràng việc sử dụng audiowaveform hay FFmpeg:

```
[INFO] Using audiowaveform for peak generation...
[SUCCESS] Used audiowaveform for peak generation
```

hoặc

```
[WARN] audiowaveform failed: error message, falling back to FFmpeg...
[SUCCESS] Used FFmpeg fallback for peak generation
```

## Tương thích

- Waveform data được tạo tương thích 100% với wavesurfer.js
- Không có thay đổi về API hoặc output format
- Có thể chuyển đổi giữa audiowaveform và FFmpeg mà không ảnh hưởng đến client
