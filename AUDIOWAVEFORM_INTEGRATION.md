# Audiowaveform Integration

## Overview

The project has been completely migrated to use [audiowaveform](https://github.com/bbc/audiowaveform) - a powerful and efficient tool for generating waveform data from audio files. Audiowaveform is developed by BBC and completely replaces FFmpeg in this project.

## Features

- **High performance**: audiowaveform is optimized for waveform data generation
- **Multiple format support**: MP3, WAV, FLAC, OGG, M4A, AAC
- **Simplified**: Completely removes FFmpeg dependencies
- **Smaller size**: Docker image is smaller without FFmpeg

## Configuration

### Environment Variables

No additional configuration needed - audiowaveform is used by default.

### Lambda Configuration Example

```json
{
  "Environment": {
    "Variables": {
      "OUTPUT_BUCKET": "your-output-bucket",
      "MEDIA_CONVERT_ROLE_ARN": "your-role-arn",
      "MEDIA_CONVERT_ENDPOINT": "your-endpoint",
      "RDS_PARAM_NAME": "your-rds-param"
    }
  }
}
```

## Cách hoạt động

### Audiowaveform Only

1. <PERSON><PERSON> thống sử dụng audiowaveform để tạo waveform data
2. Waveform data được tạo ở format tương thích với wavesurfer.js
3. Không có fallback - audiowaveform hỗ trợ đầy đủ các format cần thiết

## Functions mới

### generateAudioWaveform()

Tạo waveform data sử dụng audiowaveform binary.

```javascript
await generateAudioWaveform(inputFile, outputFile, {
    width: 1800,
    height: 280,
    zoom: 256,
    format: 'json'
});
```

### generateWaveformPeaksFromAudiowaveform()

Tạo wavesurfer-compatible peaks data từ audiowaveform JSON output.

```javascript
// Tạo file JSON (mặc định)
await generateWaveformPeaksFromAudiowaveform(inputFile, outputFile, numPeaks, 'json');

// Hoặc tạo file text
await generateWaveformPeaksFromAudiowaveform(inputFile, outputFile, numPeaks, 'text');
```

**Output JSON format:**
```json
{
  "version": 2,
  "channels": 1,
  "sample_rate": 44100,
  "samples_per_pixel": 512,
  "bits": 8,
  "length": 64,
  "data": [min1, max1, min2, max2, ...]
}
```

**Cách hoạt động:**
1. Tạo audiowaveform JSON data với width = numPeaks
2. Parse JSON và xử lý data array
3. Normalize values về range -1 đến 1
4. Format thành wavesurfer-compatible JSON hoặc text
5. Hỗ trợ nhiều format audiowaveform output khác nhau

## Build Process

Audiowaveform được build trong Docker multi-stage build:

1. **audiowaveform-builder stage**: Build audiowaveform từ source với tất cả dependencies
2. **Final stage**: Copy audiowaveform binary vào Lambda runtime

### Dependencies được build

- libid3tag
- libmad  
- FLAC
- libogg
- libvorbis
- Opus
- libgd
- libsndfile
- Boost libraries

## Troubleshooting

### Audiowaveform không hoạt động

1. Kiểm tra logs để xem error message
2. Đảm bảo file input có format được hỗ trợ (MP3, WAV, FLAC, OGG, M4A, AAC)
3. Kiểm tra file không bị corrupted

### Performance Issues

1. Audiowaveform được tối ưu hóa cho hiệu suất cao
2. Nếu gặp vấn đề với file cụ thể, kiểm tra format và quality của file

## Logs

Hệ thống sẽ log việc sử dụng audiowaveform:

```
[INFO] Generating wavesurfer peaks from original file using audiowaveform...
[SUCCESS] Used audiowaveform for peak generation
```

## Tương thích

- Waveform data được tạo tương thích 100% với wavesurfer.js
- Không có thay đổi về API hoặc output format
- Chuyển đổi hoàn toàn từ FFmpeg sang audiowaveform mà không ảnh hưởng đến client
