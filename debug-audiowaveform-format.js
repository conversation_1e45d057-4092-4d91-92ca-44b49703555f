#!/usr/bin/env node

/**
 * Script để debug format của audiowaveform JSON output
 * và so sánh với wavesurfer mergedPeaks format
 */

const fs = require('fs');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Wavesurfer mergedPeaks data mẫu từ user
const expectedPeaks = `0.0002488219179213047 -0.00019509006233420223 0.0774519219994545 -0.28983157873153687 0.09537705034017563 -0.0701386108994484 0.03840333968400955 -0.03802959993481636 0.025428406894207 -0.03369280695915222 0.09103140980005264 -0.09174150973558426 0.06238945946097374 -0.04518910124897957 0.060687433928251266 -0.050309062004089355 0.15979044139385223 -0.0853254497051239 0.06862838566303253 -0.07557408511638641 0.042503781616687775 -0.06513871252536774 0.08333148062229156 -0.06781727075576782 0.13672319054603577 -0.041148390620946884 0.037519656121730804 -0.04710478335618973 0.013540675863623619 -0.012961767613887787 0.11424784362316132 -0.021593518555164337`.split(' ').map(parseFloat);

async function createTestWavFile() {
    console.log('[DEBUG] Creating test WAV file...');
    
    const testAudioFile = '/tmp/debug_test.wav';
    const sampleRate = 44100;
    const duration = 2; // 2 seconds
    const frequency = 440;
    const samples = sampleRate * duration;
    
    // Tạo WAV header
    const wavHeader = Buffer.alloc(44);
    wavHeader.write('RIFF', 0);
    wavHeader.writeUInt32LE(36 + samples * 2, 4);
    wavHeader.write('WAVE', 8);
    wavHeader.write('fmt ', 12);
    wavHeader.writeUInt32LE(16, 16);
    wavHeader.writeUInt16LE(1, 20);
    wavHeader.writeUInt16LE(1, 22);
    wavHeader.writeUInt32LE(sampleRate, 24);
    wavHeader.writeUInt32LE(sampleRate * 2, 28);
    wavHeader.writeUInt16LE(2, 32);
    wavHeader.writeUInt16LE(16, 34);
    wavHeader.write('data', 36);
    wavHeader.writeUInt32LE(samples * 2, 40);
    
    // Tạo audio data (sine wave với một số variation)
    const audioData = Buffer.alloc(samples * 2);
    for (let i = 0; i < samples; i++) {
        // Tạo sine wave với amplitude variation để có cả positive và negative peaks
        const t = i / sampleRate;
        const amplitude = 0.5 + 0.3 * Math.sin(2 * Math.PI * 0.5 * t); // Amplitude modulation
        const sample = amplitude * Math.sin(2 * Math.PI * frequency * t) * 32767;
        audioData.writeInt16LE(Math.round(sample), i * 2);
    }
    
    // Ghi file WAV
    fs.writeFileSync(testAudioFile, Buffer.concat([wavHeader, audioData]));
    console.log(`[DEBUG] Test WAV file created: ${testAudioFile}`);
    
    return testAudioFile;
}

async function testAudiowaveformFormats() {
    console.log('=== DEBUG: Audiowaveform Format Analysis ===\n');
    
    try {
        // Tạo test file
        const testFile = await createTestWavFile();
        
        // Test với các width khác nhau
        const testConfigs = [
            { width: 64, description: '64 width (32 peaks)' },
            { width: 128, description: '128 width (64 peaks)' },
            { width: 32, description: '32 width (16 peaks)' }
        ];
        
        for (const config of testConfigs) {
            console.log(`\n[TEST] ${config.description}`);
            
            const jsonFile = `/tmp/debug_${config.width}.json`;
            
            // Generate audiowaveform JSON
            const command = [
                'audiowaveform',
                '-i', `"${testFile}"`,
                '-o', `"${jsonFile}"`,
                '--width', config.width.toString(),
                '--height', '256',
                '--output-format', 'json'
            ].join(' ');
            
            console.log(`[DEBUG] Command: ${command}`);
            
            try {
                await execAsync(command);
                
                // Đọc và phân tích JSON
                const jsonData = JSON.parse(fs.readFileSync(jsonFile, 'utf8'));
                
                console.log(`[DEBUG] JSON structure:`);
                console.log(`  - version: ${jsonData.version}`);
                console.log(`  - channels: ${jsonData.channels}`);
                console.log(`  - sample_rate: ${jsonData.sample_rate}`);
                console.log(`  - samples_per_pixel: ${jsonData.samples_per_pixel}`);
                console.log(`  - bits: ${jsonData.bits}`);
                console.log(`  - length: ${jsonData.length}`);
                console.log(`  - data array length: ${jsonData.data.length}`);
                
                // Phân tích data array
                const data = jsonData.data;
                console.log(`[DEBUG] First 20 data values:`, data.slice(0, 20));
                
                // Kiểm tra range của values
                const minVal = Math.min(...data);
                const maxVal = Math.max(...data);
                console.log(`[DEBUG] Data range: ${minVal} to ${maxVal}`);
                
                // Kiểm tra xem có phải min/max pairs không
                console.log(`[DEBUG] Checking min/max pattern:`);
                for (let i = 0; i < Math.min(10, data.length); i += 2) {
                    const val1 = data[i];
                    const val2 = data[i + 1];
                    console.log(`  Pair ${i/2 + 1}: ${val1}, ${val2} (${val1 <= val2 ? 'min,max' : 'NOT min,max'})`);
                }
                
                // Cleanup
                fs.unlinkSync(jsonFile);
                
            } catch (error) {
                console.error(`[ERROR] Failed to process ${config.description}: ${error.message}`);
            }
        }
        
        // So sánh với expected peaks
        console.log(`\n[ANALYSIS] Expected wavesurfer peaks analysis:`);
        console.log(`  - Total values: ${expectedPeaks.length}`);
        console.log(`  - Number of peaks: ${expectedPeaks.length / 2}`);
        console.log(`  - Range: ${Math.min(...expectedPeaks)} to ${Math.max(...expectedPeaks)}`);
        console.log(`  - First 10 values:`, expectedPeaks.slice(0, 10));
        
        // Kiểm tra min/max pattern trong expected data
        console.log(`[ANALYSIS] Expected peaks min/max pattern:`);
        for (let i = 0; i < Math.min(10, expectedPeaks.length); i += 2) {
            const val1 = expectedPeaks[i];
            const val2 = expectedPeaks[i + 1];
            console.log(`  Pair ${i/2 + 1}: ${val1}, ${val2} (${val1 <= val2 ? 'min,max' : 'NOT min,max'})`);
        }
        
        // Cleanup
        fs.unlinkSync(testFile);
        
        console.log('\n=== Analysis Complete ===');
        
    } catch (error) {
        console.error(`[ERROR] Debug failed: ${error.message}`);
    }
}

// Chạy debug
if (require.main === module) {
    testAudiowaveformFormats();
}

module.exports = { testAudiowaveformFormats };
