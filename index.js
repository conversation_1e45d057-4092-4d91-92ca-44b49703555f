const AWS = require('aws-sdk');
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Configuration
const OUTPUT_BUCKET = process.env.OUTPUT_BUCKET;
const MEDIA_CONVERT_ROLE_ARN = process.env.MEDIA_CONVERT_ROLE_ARN;
const MEDIA_CONVERT_ENDPOINT = process.env.MEDIA_CONVERT_ENDPOINT;
const RDS_PARAM_NAME = process.env.RDS_PARAM_NAME;

// Initialize AWS clients
const s3 = new AWS.S3();
const mediaConvert = new AWS.MediaConvert({ endpoint: MEDIA_CONVERT_ENDPOINT });
const ssm = new AWS.SSM();

// Supported file formats
const AUDIO_EXTENSIONS = ['.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg'];
const VIDEO_EXTENSIONS = ['.mp4', '.mov', '.mkv', '.avi', '.webm', '.mpg'];

let dbConnection = null;

async function getDatabaseCredentials() {
    const response = await ssm.getParameter({
        Name: RDS_PARAM_NAME,
        WithDecryption: true
    }).promise();

    return JSON.parse(response.Parameter.Value);
}

async function getDbConnection() {
    if (dbConnection) {
        return dbConnection;
    }

    const credentials = await getDatabaseCredentials();
    dbConnection = await mysql.createConnection({
        host: credentials.db_host,
        port: credentials.db_port,
        user: credentials.db_username,
        password: credentials.db_password,
        database: credentials.db_name
    });

    return dbConnection;
}

async function generateAudioWaveform(inputFile, outputFile, options = {}) {
    console.log(`[INFO] Generating waveform using audiowaveform...`);

    const {
        width = 1800,
        height = 280,
        zoom = 256,
        format = 'json'
    } = options;

    try {
        const command = [
            'audiowaveform',
            '-i', `"${inputFile}"`,
            '-o', `"${outputFile}"`,
            '--width', width.toString(),
            '--height', height.toString(),
            '--zoom', zoom.toString()
        ];

        if (format === 'json') {
            command.push('--output-format', 'json');
        } else if (format === 'dat') {
            command.push('--output-format', 'dat');
        }

        const commandString = command.join(' ');
        console.log(`[INFO] Executing: ${commandString}`);

        const { stdout, stderr } = await execAsync(commandString);

        if (stderr) {
            console.log(`[WARN] audiowaveform stderr: ${stderr}`);
        }

        if (stdout) {
            console.log(`[INFO] audiowaveform stdout: ${stdout}`);
        }

        console.log(`[SUCCESS] Waveform generated successfully: ${outputFile}`);
        return outputFile;

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform: ${error.message}`);
        throw error;
    }
}

async function generateWaveformPeaksFromAudiowaveform(inputFile, outputFile, numPeaks = 32, outputFormat = 'json') {
    console.log(`[INFO] Generating waveform peaks using audiowaveform (format: ${outputFormat})...`);

    try {
        const tempJsonFile = `/tmp/temp_waveform_${Date.now()}.json`;

        await generateAudioWaveform(inputFile, tempJsonFile, {
            width: numPeaks,
            format: 'json'
        });

        const jsonData = JSON.parse(fs.readFileSync(tempJsonFile, 'utf8'));

        if (!jsonData.data || !Array.isArray(jsonData.data)) {
            throw new Error('Invalid waveform JSON format');
        }

        console.log(`[DEBUG] Audiowaveform JSON data length: ${jsonData.data.length}`);
        console.log(`[DEBUG] Expected peaks: ${numPeaks}, Data points: ${jsonData.data.length}`);

        const waveformData = jsonData.data;
        const mergedPeaks = [];

        if (waveformData.length === numPeaks * 2) {
            console.log(`[DEBUG] Using min/max pairs format`);
            for (let i = 0; i < waveformData.length; i += 2) {
                const min = waveformData[i] || 0;
                const max = waveformData[i + 1] || 0;

                const normalizedMin = min / (Math.pow(2, jsonData.bits - 1));
                const normalizedMax = max / (Math.pow(2, jsonData.bits - 1));

                mergedPeaks.push(normalizedMin);
                mergedPeaks.push(normalizedMax);
            }
        } else {
            console.log(`[DEBUG] Using alternative format, creating min/max from single values`);
            const samplesPerPeak = Math.floor(waveformData.length / numPeaks);

            for (let i = 0; i < numPeaks; i++) {
                const startIdx = i * samplesPerPeak;
                const endIdx = Math.min(startIdx + samplesPerPeak, waveformData.length);

                let min = Infinity;
                let max = -Infinity;

                for (let j = startIdx; j < endIdx; j++) {
                    const value = waveformData[j] / (Math.pow(2, jsonData.bits - 1));
                    min = Math.min(min, value);
                    max = Math.max(max, value);
                }

                mergedPeaks.push(min === Infinity ? 0 : min);
                mergedPeaks.push(max === -Infinity ? 0 : max);
            }
        }

        const formattedPeaks = mergedPeaks.map(peak => {
            return Math.round(peak * Math.pow(10, 8)) / Math.pow(10, 8);
        });

        console.log(`[DEBUG] Generated ${formattedPeaks.length} peak values (${formattedPeaks.length/2} peaks)`);
        console.log(`[DEBUG] First few peaks: ${formattedPeaks.slice(0, 8).join(', ')}`);

        if (outputFormat === 'json') {
            const wavesurferJson = {
                version: 2,
                channels: jsonData.channels || 1,
                sample_rate: jsonData.sample_rate || 44100,
                samples_per_pixel: jsonData.samples_per_pixel || 512,
                bits: 8,
                length: formattedPeaks.length,
                data: formattedPeaks
            };

            fs.writeFileSync(outputFile, JSON.stringify(wavesurferJson, null, 2));
            console.log(`[SUCCESS] JSON waveform peaks generated: ${outputFile}`);
        } else {
            const peaksString = formattedPeaks.map(peak => peak.toString()).join(' ');
            fs.writeFileSync(outputFile, peaksString);
            console.log(`[SUCCESS] Text waveform peaks generated: ${outputFile}`);
        }

        if (fs.existsSync(tempJsonFile)) {
            fs.unlinkSync(tempJsonFile);
        }

        return outputFile;

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform peaks: ${error.message}`);
        throw error;
    }
}

exports.startConversionHandler = async(event, context) => {
    console.log(`[START] Start conversion handler`);

    try {
        const record = event.Records[0];
        const sourceBucket = record.s3.bucket.name;
        const sourceKey = decodeURIComponent(record.s3.object.key.replace(/\+/g, ' '));

        console.log(`[START] Processing file: s3://${sourceBucket}/${sourceKey}`);

        const basePathWithoutExt = path.parse(sourceKey).dir + '/' + path.parse(sourceKey).name;
        const destinationFolder = `s3://${OUTPUT_BUCKET}/${basePathWithoutExt}/`;

        console.log(`[INFO] Destination folder: ${destinationFolder}`);

        const extension = path.extname(sourceKey).toLowerCase();
        let fileType = 'unknown';
        if (VIDEO_EXTENSIONS.includes(extension)) {
            fileType = 'video';
        } else if (AUDIO_EXTENSIONS.includes(extension)) {
            fileType = 'audio';
        }

        console.log(`[INFO] File type detected: ${fileType} (extension: ${extension})`);

        if (fileType === 'unknown') {
            console.log(`[ERROR] Unsupported file type: ${extension}. Skipping.`);
            return { statusCode: 200, body: 'Unsupported file type' };
        }

        if (fileType === 'audio') {
            console.log(`[INFO] Audio file detected, generating peaks directly...`);

            try {
                const s3Object = await s3.getObject({
                    Bucket: sourceBucket,
                    Key: sourceKey
                }).promise();

                const localInput = `/tmp/${path.basename(sourceKey)}`;
                const basePath = path.parse(sourceKey);
                const wavesurferJsonS3Key = `${basePath.dir}/${basePath.name}_peaks.json`;
                const localJsonOutput = `/tmp/${path.basename(wavesurferJsonS3Key)}`;

                fs.writeFileSync(localInput, s3Object.Body);

                console.log(`[INFO] Generating wavesurfer peaks JSON from original file using audiowaveform...`);
                await generateWaveformPeaksFromAudiowaveform(localInput, localJsonOutput, 32, 'json');
                console.log(`[SUCCESS] JSON peaks generated`);

                console.log(`[INFO] Uploading peaks JSON to S3...`);
                await s3.upload({
                    Bucket: OUTPUT_BUCKET,
                    Key: wavesurferJsonS3Key,
                    Body: fs.readFileSync(localJsonOutput),
                    ContentType: 'application/json'
                }).promise();

                [localInput, localJsonOutput].forEach(file => {
                    if (fs.existsSync(file)) {
                        fs.unlinkSync(file);
                    }
                });

                const connection = await getDbConnection();
                const now = new Date();

                await connection.execute(
                    'INSERT INTO app_mediaconvertjob (original_object_key, status, waveform_data_key, created, modified) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE status = ?, waveform_data_key = ?, modified = ?', [sourceKey, 'COMPLETE', wavesurferJsonS3Key, now, now, 'COMPLETE', wavesurferJsonS3Key, now]
                );

                console.log(`[SUCCESS] Audio file processed directly - Peaks JSON: ${wavesurferJsonS3Key}`);
                return { statusCode: 200, body: 'Audio file processed directly' };
            } catch (error) {
                console.error(`[ERROR] Failed to process audio file directly: ${error.message}`);

                const connection = await getDbConnection();
                const now = new Date();

                await connection.execute(
                    'INSERT INTO app_mediaconvertjob (original_object_key, status, error_message, created, modified) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE status = ?, error_message = ?, modified = ?', [sourceKey, 'FAILED', error.message, now, now, 'FAILED', error.message, now]
                );

                throw error;
            }
        }

        console.log(`[INFO] Video file detected, using MediaConvert...`);

        const inputSettings = {
            AudioSelectors: { "Audio Selector 1": { DefaultSelection: "DEFAULT" } },
            FileInput: `s3://${sourceBucket}/${sourceKey}`
        };

        const nameModifier = "-converted";

        const hlsOutputGroup = {
            Name: "Apple HLS",
            Outputs: [{ Preset: "System-Avc_16x9_720p_29_97fps_3500kbps", NameModifier: nameModifier }],
            OutputGroupSettings: {
                Type: "HLS_GROUP_SETTINGS",
                HlsGroupSettings: { SegmentLength: 10, Destination: destinationFolder, MinSegmentLength: 0 }
            }
        };

        const jobSettings = { Inputs: [inputSettings], OutputGroups: [hlsOutputGroup] };

        console.log(`[INFO] Creating MediaConvert job for video file...`);
        const mcResponse = await mediaConvert.createJob({
            Role: MEDIA_CONVERT_ROLE_ARN,
            Settings: jobSettings,
            UserMetadata: { 'original_object_key': sourceKey }
        }).promise();

        const jobId = mcResponse.Job.Id;
        console.log(`[SUCCESS] MediaConvert job created with ID: ${jobId}`);

        const connection = await getDbConnection();
        const now = new Date();

        // Check if record exists
        const [existingRecords] = await connection.execute(
            'SELECT id, status FROM app_mediaconvertjob WHERE original_object_key = ?', [sourceKey]
        );

        if (existingRecords.length > 0) {
            const { id, status } = existingRecords[0];
            console.log(`[INFO] Record already exists (ID: ${id}, Status: ${status})`);

            if (!['COMPLETE', 'PROCESSING'].includes(status)) {
                await connection.execute(
                    'UPDATE app_mediaconvertjob SET status = ?, job_id = ?, error_message = NULL, modified = ? WHERE original_object_key = ?', ['PROCESSING', jobId, now, sourceKey]
                );
                console.log(`[SUCCESS] Updated existing record - Status: PROCESSING, Job ID: ${jobId}`);
            }
        } else {
            await connection.execute(
                'INSERT INTO app_mediaconvertjob (original_object_key, status, job_id, created, modified) VALUES (?, ?, ?, ?, ?)', [sourceKey, 'PROCESSING', jobId, now, now]
            );
            console.log(`[SUCCESS] New database record created - Status: PROCESSING, Job ID: ${jobId}`);
        }

        console.log(`[END] Completed processing for: ${sourceKey}`);
        return { statusCode: 200, body: 'Job created successfully' };

    } catch (error) {
        console.error(`[ERROR] Start conversion failed: ${error.message}`);

        try {
            const connection = await getDbConnection();
            const sourceKey = event.Records[0].s3.object.key;
            const now = new Date();

            await connection.execute(
                'INSERT INTO app_mediaconvertjob (original_object_key, status, error_message, created, modified) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE status = ?, error_message = ?, modified = ?', [sourceKey, 'FAILED', error.message, now, now, 'FAILED', error.message, now]
            );
        } catch (dbError) {
            console.error(`[ERROR] Failed to update database: ${dbError.message}`);
        }

        throw error;
    }
};

// =================================================================
// HANDLER 2: Xử lý kết quả (Được trigger bởi EventBridge)
// =================================================================
exports.handleCompletionHandler = async(event, context) => {
    console.log(`[START] Job completion handler`);

    try {
        const jobStatus = event.detail.status;
        const userMetadata = event.detail.userMetadata;
        const originalKey = userMetadata.original_object_key;

        console.log(`[START] Job completion handler - File: ${originalKey}, Status: ${jobStatus}`);

        const connection = await getDbConnection();
        const now = new Date();

        if (jobStatus === 'COMPLETE') {
            console.log(`[INFO] Processing COMPLETE status for: ${originalKey}`);

            const outputDetails = event.detail.outputGroupDetails[0].outputDetails[0];
            const outputKeyFull = outputDetails.outputFilePaths[0];
            const outputKey = outputKeyFull.split('/').slice(3).join('/');

            console.log(`[INFO] Output file key: ${outputKey}`);

            // Video files chỉ tạo HLS, không cần generate peaks
            const nextStatus = 'COMPLETE';

            console.log(`[INFO] Next status: ${nextStatus} (video file completed)`);

            await connection.execute(
                'UPDATE app_mediaconvertjob SET status = ?, converted_media_key = ?, modified = ? WHERE original_object_key = ?', [nextStatus, outputKey, now, originalKey]
            );

            console.log(`[SUCCESS] Database updated - Status: ${nextStatus}, Output: ${outputKey}`);

        } else if (jobStatus === 'ERROR') {
            console.log(`[INFO] Processing ERROR status for: ${originalKey}`);

            const errorMessage = event.detail.errorMessage || 'Unknown error';
            console.log(`[ERROR] Job error message: ${errorMessage}`);

            await connection.execute(
                'UPDATE app_mediaconvertjob SET status = ?, error_message = ?, modified = ? WHERE original_object_key = ?', ['FAILED', errorMessage, now, originalKey]
            );

            console.log(`[SUCCESS] Database updated - Status: FAILED`);
        }

        console.log(`[END] Completed job completion handler for: ${originalKey}`);
        return { statusCode: 200, body: 'Database updated' };

    } catch (error) {
        console.error(`[ERROR] Handle completion failed: ${error.message}`);
        throw error;
    }
};

// Handler 3 đã được loại bỏ vì không còn cần thiết
// Audio files được xử lý trực tiếp trong handler 1
// Video files chỉ tạo HLS, không cần generate peaks