const AWS = require('aws-sdk');
const mysql = require('mysql2/promise');
const ffmpeg = require('fluent-ffmpeg');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// --- <PERSON><PERSON><PERSON> hình ---
const OUTPUT_BUCKET = process.env.OUTPUT_BUCKET;
const MEDIA_CONVERT_ROLE_ARN = process.env.MEDIA_CONVERT_ROLE_ARN;
const MEDIA_CONVERT_ENDPOINT = process.env.MEDIA_CONVERT_ENDPOINT;
const RDS_PARAM_NAME = process.env.RDS_PARAM_NAME;
const USE_AUDIOWAVEFORM = process.env.USE_AUDIOWAVEFORM === 'true'; // Default: false (sử dụng FFmpeg)

// Khởi tạo AWS clients
const s3 = new AWS.S3();
const mediaConvert = new AWS.MediaConvert({ endpoint: MEDIA_CONVERT_ENDPOINT });
const ssm = new AWS.SSM();

// Danh sách các định dạng file được hỗ trợ
const AUDIO_EXTENSIONS = ['.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg'];
const VIDEO_EXTENSIONS = ['.mp4', '.mov', '.mkv', '.avi', '.webm', '.mpg'];

let dbConnection = null;

// Database helper functions
async function getDatabaseCredentials() {
    const response = await ssm.getParameter({
        Name: RDS_PARAM_NAME,
        WithDecryption: true
    }).promise();

    return JSON.parse(response.Parameter.Value);
}

async function getDbConnection() {
    if (dbConnection) {
        return dbConnection;
    }

    const credentials = await getDatabaseCredentials();
    dbConnection = await mysql.createConnection({
        host: credentials.db_host,
        port: credentials.db_port,
        user: credentials.db_username,
        password: credentials.db_password,
        database: credentials.db_name
    });

    return dbConnection;
}

// Hàm tạo wavesurfer peaks sử dụng FFmpeg (convert sang WAV tạm thời)
async function generateWaveSurferMergedPeaks(inputFile, outputFile, numPeaks = 32) {
    console.log(`[INFO] Generating wavesurfer mergedPeaks using FFmpeg...`);

    return new Promise((resolve, reject) => {
        // Tạo file WAV tạm thời
        const tempWavFile = `/tmp/temp_audio_${Date.now()}.wav`;

        // Sử dụng FFmpeg để convert sang WAV trước
        ffmpeg(inputFile)
            .audioChannels(1)
            .audioFrequency(44100)
            .audioCodec('pcm_s16le')
            .on('end', () => {
                try {
                    // Đọc file WAV và extract samples
                    const wavBuffer = fs.readFileSync(tempWavFile);

                    // Skip WAV header (44 bytes)
                    const samples = [];
                    const dataStart = 44;

                    for (let i = dataStart; i < wavBuffer.length; i += 2) {
                        // Read 16-bit PCM
                        const sample = wavBuffer.readInt16LE(i) / 32768.0;
                        samples.push(sample);
                    }

                    const totalSamples = samples.length;
                    const samplesPerPeak = Math.floor(totalSamples / numPeaks);

                    console.log(`[INFO] Audio loaded - Samples: ${totalSamples}, Duration: ${(totalSamples / 44100).toFixed(2)}s`);

                    const mergedPeaks = [];

                    // Tính mergedPeaks (min, max pairs) - giống hệt wavesurfer.js
                    for (let i = 0; i < numPeaks; i++) {
                        const startIdx = i * samplesPerPeak;
                        const endIdx = Math.min(startIdx + samplesPerPeak, totalSamples);

                        let min = 1;
                        let max = 0;

                        for (let j = startIdx; j < endIdx; j++) {
                            const sample = samples[j];
                            min = Math.min(min, sample);
                            max = Math.max(max, sample);
                        }

                        // Đảm bảo min <= 0 và max >= 0
                        min = Math.min(min, 0);
                        max = Math.max(max, 0);

                        mergedPeaks.push(min, max);
                    }

                    // Format mergedPeaks - tương thích với wavesurfer.backend.mergedPeaks
                    const peaksString = mergedPeaks.map(peak => {
                        const formattedPeak = Math.round(peak * Math.pow(10, 8)) / Math.pow(10, 8);
                        return formattedPeak.toString();
                    }).join(' ');

                    // Ghi file
                    fs.writeFileSync(outputFile, peaksString);

                    // Cleanup temp file
                    if (fs.existsSync(tempWavFile)) {
                        fs.unlinkSync(tempWavFile);
                    }

                    console.log(`[SUCCESS] Wavesurfer mergedPeaks generated successfully`);
                    resolve();

                } catch (error) {
                    console.error(`[ERROR] Failed to process audio: ${error.message}`);
                    // Cleanup temp file on error
                    if (fs.existsSync(tempWavFile)) {
                        fs.unlinkSync(tempWavFile);
                    }
                    reject(error);
                }
            })
            .on('error', (error) => {
                console.error(`[ERROR] FFmpeg error: ${error.message}`);
                // Cleanup temp file on error
                if (fs.existsSync(tempWavFile)) {
                    fs.unlinkSync(tempWavFile);
                }
                reject(error);
            })
            .save(tempWavFile); // Save as WAV file
    });
}

// Hàm tạo waveform data sử dụng audiowaveform
async function generateAudioWaveform(inputFile, outputFile, options = {}) {
    console.log(`[INFO] Generating waveform using audiowaveform...`);

    const {
        width = 1800,
        height = 280,
        zoom = 256,
        format = 'json'
    } = options;

    try {
        // Tạo command cho audiowaveform
        const command = [
            'audiowaveform',
            '-i', `"${inputFile}"`,
            '-o', `"${outputFile}"`,
            '--width', width.toString(),
            '--height', height.toString(),
            '--zoom', zoom.toString()
        ];

        if (format === 'json') {
            command.push('--output-format', 'json');
        } else if (format === 'dat') {
            command.push('--output-format', 'dat');
        }

        const commandString = command.join(' ');
        console.log(`[INFO] Executing: ${commandString}`);

        const { stdout, stderr } = await execAsync(commandString);

        if (stderr) {
            console.log(`[WARN] audiowaveform stderr: ${stderr}`);
        }

        if (stdout) {
            console.log(`[INFO] audiowaveform stdout: ${stdout}`);
        }

        console.log(`[SUCCESS] Waveform generated successfully: ${outputFile}`);
        return outputFile;

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform: ${error.message}`);
        throw error;
    }
}

// Hàm tạo waveform peaks từ audiowaveform JSON data
async function generateWaveformPeaksFromAudiowaveform(inputFile, outputFile, numPeaks = 32) {
    console.log(`[INFO] Generating waveform peaks using audiowaveform...`);

    try {
        // Tạo file JSON tạm thời
        const tempJsonFile = `/tmp/temp_waveform_${Date.now()}.json`;

        // Generate waveform JSON data
        await generateAudioWaveform(inputFile, tempJsonFile, {
            width: numPeaks * 2, // Mỗi peak có min/max
            format: 'json'
        });

        // Đọc và parse JSON data
        const jsonData = JSON.parse(fs.readFileSync(tempJsonFile, 'utf8'));

        if (!jsonData.data || !Array.isArray(jsonData.data)) {
            throw new Error('Invalid waveform JSON format');
        }

        // Convert audiowaveform data thành wavesurfer mergedPeaks format
        const waveformData = jsonData.data;
        const mergedPeaks = [];

        // audiowaveform trả về array các giá trị, cần group thành min/max pairs
        for (let i = 0; i < waveformData.length; i += 2) {
            const min = (waveformData[i] || 0) / 32768.0; // Normalize từ 16-bit
            const max = (waveformData[i + 1] || 0) / 32768.0;

            mergedPeaks.push(Math.min(min, 0)); // Đảm bảo min <= 0
            mergedPeaks.push(Math.max(max, 0)); // Đảm bảo max >= 0
        }

        // Format peaks string tương thích với wavesurfer
        const peaksString = mergedPeaks.map(peak => {
            const formattedPeak = Math.round(peak * Math.pow(10, 8)) / Math.pow(10, 8);
            return formattedPeak.toString();
        }).join(' ');

        // Ghi file output
        fs.writeFileSync(outputFile, peaksString);

        // Cleanup temp file
        if (fs.existsSync(tempJsonFile)) {
            fs.unlinkSync(tempJsonFile);
        }

        console.log(`[SUCCESS] Waveform peaks generated successfully using audiowaveform`);
        return outputFile;

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform peaks: ${error.message}`);
        throw error;
    }
}

// =================================================================
// HANDLER 1: Bắt đầu Job (Được trigger bởi S3 bucket đầu vào)
// =================================================================
exports.startConversionHandler = async(event, context) => {
    console.log(`[START] Start conversion handler`);

    try {
        const record = event.Records[0];
        const sourceBucket = record.s3.bucket.name;
        const sourceKey = decodeURIComponent(record.s3.object.key.replace(/\+/g, ' '));

        console.log(`[START] Processing file: s3://${sourceBucket}/${sourceKey}`);

        // Tạo đường dẫn đích
        const basePathWithoutExt = path.parse(sourceKey).dir + '/' + path.parse(sourceKey).name;
        const destinationFolder = `s3://${OUTPUT_BUCKET}/${basePathWithoutExt}/`;

        console.log(`[INFO] Destination folder: ${destinationFolder}`);

        // Phát hiện loại file
        const extension = path.extname(sourceKey).toLowerCase();
        let fileType = 'unknown';
        if (VIDEO_EXTENSIONS.includes(extension)) {
            fileType = 'video';
        } else if (AUDIO_EXTENSIONS.includes(extension)) {
            fileType = 'audio';
        }

        console.log(`[INFO] File type detected: ${fileType} (extension: ${extension})`);

        if (fileType === 'unknown') {
            console.log(`[ERROR] Unsupported file type: ${extension}. Skipping.`);
            return { statusCode: 200, body: 'Unsupported file type' };
        }

        // Nếu là audio file, generate peaks trực tiếp
        if (fileType === 'audio') {
            console.log(`[INFO] Audio file detected, generating peaks directly...`);

            try {
                // Download file từ S3
                const s3Object = await s3.getObject({
                    Bucket: sourceBucket,
                    Key: sourceKey
                }).promise();

                const localInput = `/tmp/${path.basename(sourceKey)}`;
                const basePath = path.parse(sourceKey);
                const wavesurferPeaksS3Key = `${basePath.dir}/${basePath.name}_peaks.txt`;
                const localPeaksOutput = `/tmp/${path.basename(wavesurferPeaksS3Key)}`;

                // Lưu file tạm
                fs.writeFileSync(localInput, s3Object.Body);

                // Generate peaks trực tiếp từ file gốc
                console.log(`[INFO] Generating wavesurfer peaks from original file...`);

                if (USE_AUDIOWAVEFORM) {
                    console.log(`[INFO] Using audiowaveform for peak generation...`);
                    try {
                        await generateWaveformPeaksFromAudiowaveform(localInput, localPeaksOutput);
                        console.log(`[SUCCESS] Used audiowaveform for peak generation`);
                    } catch (audiowaveformError) {
                        console.log(`[WARN] audiowaveform failed: ${audiowaveformError.message}, falling back to FFmpeg...`);
                        // Fallback về FFmpeg nếu audiowaveform thất bại
                        await generateWaveSurferMergedPeaks(localInput, localPeaksOutput);
                        console.log(`[SUCCESS] Used FFmpeg fallback for peak generation`);
                    }
                } else {
                    console.log(`[INFO] Using FFmpeg for peak generation...`);
                    await generateWaveSurferMergedPeaks(localInput, localPeaksOutput);
                    console.log(`[SUCCESS] Used FFmpeg for peak generation`);
                }

                // Upload peaks lên S3
                console.log(`[INFO] Uploading peaks to S3...`);
                await s3.upload({
                    Bucket: OUTPUT_BUCKET,
                    Key: wavesurferPeaksS3Key,
                    Body: fs.readFileSync(localPeaksOutput),
                    ContentType: 'text/plain'
                }).promise();

                // Cleanup
                [localInput, localPeaksOutput].forEach(file => {
                    if (fs.existsSync(file)) {
                        fs.unlinkSync(file);
                    }
                });

                // Update database với status COMPLETE
                const connection = await getDbConnection();
                const now = new Date();

                await connection.execute(
                    'INSERT INTO app_mediaconvertjob (original_object_key, status, waveform_data_key, created, modified) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE status = ?, waveform_data_key = ?, modified = ?', [sourceKey, 'COMPLETE', wavesurferPeaksS3Key, now, now, 'COMPLETE', wavesurferPeaksS3Key, now]
                );

                console.log(`[SUCCESS] Audio file processed directly - Peaks: ${wavesurferPeaksS3Key}`);
                return { statusCode: 200, body: 'Audio file processed directly' };
            } catch (error) {
                console.error(`[ERROR] Failed to process audio file directly: ${error.message}`);

                // Update database với status FAILED
                const connection = await getDbConnection();
                const now = new Date();

                await connection.execute(
                    'INSERT INTO app_mediaconvertjob (original_object_key, status, error_message, created, modified) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE status = ?, error_message = ?, modified = ?', [sourceKey, 'FAILED', error.message, now, now, 'FAILED', error.message, now]
                );

                throw error;
            }
        }

        // Nếu là video file, vẫn dùng MediaConvert như cũ
        console.log(`[INFO] Video file detected, using MediaConvert...`);

        // Tạo MediaConvert job settings cho video
        const inputSettings = {
            AudioSelectors: { "Audio Selector 1": { DefaultSelection: "DEFAULT" } },
            FileInput: `s3://${sourceBucket}/${sourceKey}`
        };

        const nameModifier = "-converted";

        const hlsOutputGroup = {
            Name: "Apple HLS",
            Outputs: [{ Preset: "System-Avc_16x9_720p_29_97fps_3500kbps", NameModifier: nameModifier }],
            OutputGroupSettings: {
                Type: "HLS_GROUP_SETTINGS",
                HlsGroupSettings: { SegmentLength: 10, Destination: destinationFolder, MinSegmentLength: 0 }
            }
        };

        const jobSettings = { Inputs: [inputSettings], OutputGroups: [hlsOutputGroup] };

        // Tạo MediaConvert job cho video
        console.log(`[INFO] Creating MediaConvert job for video file...`);
        const mcResponse = await mediaConvert.createJob({
            Role: MEDIA_CONVERT_ROLE_ARN,
            Settings: jobSettings,
            UserMetadata: { 'original_object_key': sourceKey }
        }).promise();

        const jobId = mcResponse.Job.Id;
        console.log(`[SUCCESS] MediaConvert job created with ID: ${jobId}`);

        // Update database
        const connection = await getDbConnection();
        const now = new Date();

        // Check if record exists
        const [existingRecords] = await connection.execute(
            'SELECT id, status FROM app_mediaconvertjob WHERE original_object_key = ?', [sourceKey]
        );

        if (existingRecords.length > 0) {
            const { id, status } = existingRecords[0];
            console.log(`[INFO] Record already exists (ID: ${id}, Status: ${status})`);

            if (!['COMPLETE', 'PROCESSING'].includes(status)) {
                await connection.execute(
                    'UPDATE app_mediaconvertjob SET status = ?, job_id = ?, error_message = NULL, modified = ? WHERE original_object_key = ?', ['PROCESSING', jobId, now, sourceKey]
                );
                console.log(`[SUCCESS] Updated existing record - Status: PROCESSING, Job ID: ${jobId}`);
            }
        } else {
            await connection.execute(
                'INSERT INTO app_mediaconvertjob (original_object_key, status, job_id, created, modified) VALUES (?, ?, ?, ?, ?)', [sourceKey, 'PROCESSING', jobId, now, now]
            );
            console.log(`[SUCCESS] New database record created - Status: PROCESSING, Job ID: ${jobId}`);
        }

        console.log(`[END] Completed processing for: ${sourceKey}`);
        return { statusCode: 200, body: 'Job created successfully' };

    } catch (error) {
        console.error(`[ERROR] Start conversion failed: ${error.message}`);

        try {
            const connection = await getDbConnection();
            const sourceKey = event.Records[0].s3.object.key;
            const now = new Date();

            await connection.execute(
                'INSERT INTO app_mediaconvertjob (original_object_key, status, error_message, created, modified) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE status = ?, error_message = ?, modified = ?', [sourceKey, 'FAILED', error.message, now, now, 'FAILED', error.message, now]
            );
        } catch (dbError) {
            console.error(`[ERROR] Failed to update database: ${dbError.message}`);
        }

        throw error;
    }
};

// =================================================================
// HANDLER 2: Xử lý kết quả (Được trigger bởi EventBridge)
// =================================================================
exports.handleCompletionHandler = async(event, context) => {
    console.log(`[START] Job completion handler`);

    try {
        const jobStatus = event.detail.status;
        const userMetadata = event.detail.userMetadata;
        const originalKey = userMetadata.original_object_key;

        console.log(`[START] Job completion handler - File: ${originalKey}, Status: ${jobStatus}`);

        const connection = await getDbConnection();
        const now = new Date();

        if (jobStatus === 'COMPLETE') {
            console.log(`[INFO] Processing COMPLETE status for: ${originalKey}`);

            const outputDetails = event.detail.outputGroupDetails[0].outputDetails[0];
            const outputKeyFull = outputDetails.outputFilePaths[0];
            const outputKey = outputKeyFull.split('/').slice(3).join('/');

            console.log(`[INFO] Output file key: ${outputKey}`);

            // Video files chỉ tạo HLS, không cần generate peaks
            const nextStatus = 'COMPLETE';

            console.log(`[INFO] Next status: ${nextStatus} (video file completed)`);

            await connection.execute(
                'UPDATE app_mediaconvertjob SET status = ?, converted_media_key = ?, modified = ? WHERE original_object_key = ?', [nextStatus, outputKey, now, originalKey]
            );

            console.log(`[SUCCESS] Database updated - Status: ${nextStatus}, Output: ${outputKey}`);

        } else if (jobStatus === 'ERROR') {
            console.log(`[INFO] Processing ERROR status for: ${originalKey}`);

            const errorMessage = event.detail.errorMessage || 'Unknown error';
            console.log(`[ERROR] Job error message: ${errorMessage}`);

            await connection.execute(
                'UPDATE app_mediaconvertjob SET status = ?, error_message = ?, modified = ? WHERE original_object_key = ?', ['FAILED', errorMessage, now, originalKey]
            );

            console.log(`[SUCCESS] Database updated - Status: FAILED`);
        }

        console.log(`[END] Completed job completion handler for: ${originalKey}`);
        return { statusCode: 200, body: 'Database updated' };

    } catch (error) {
        console.error(`[ERROR] Handle completion failed: ${error.message}`);
        throw error;
    }
};

// Handler 3 đã được loại bỏ vì không còn cần thiết
// Audio files được xử lý trực tiếp trong handler 1
// Video files chỉ tạo HLS, không cần generate peaks