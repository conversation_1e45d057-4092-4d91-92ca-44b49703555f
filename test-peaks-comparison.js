#!/usr/bin/env node

/**
 * Script để test và so sánh peaks output với expected data
 */

const fs = require('fs');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Expected peaks data từ user
const expectedPeaksString = `0.0002488219179213047 -0.00019509006233420223 0.0774519219994545 -0.28983157873153687 0.09537705034017563 -0.0701386108994484 0.03840333968400955 -0.03802959993481636 0.025428406894207 -0.03369280695915222 0.09103140980005264 -0.09174150973558426 0.06238945946097374 -0.04518910124897957 0.060687433928251266 -0.050309062004089355 0.15979044139385223 -0.0853254497051239 0.06862838566303253 -0.07557408511638641 0.042503781616687775 -0.06513871252536774 0.08333148062229156 -0.06781727075576782 0.13672319054603577 -0.041148390620946884 0.037519656121730804 -0.04710478335618973 0.013540675863623619 -0.012961767613887787 0.11424784362316132 -0.021593518555164337`;

const expectedPeaks = expectedPeaksString.split(' ').map(parseFloat);

// Copy functions từ index.js
async function generateAudioWaveform(inputFile, outputFile, options = {}) {
    console.log(`[INFO] Generating waveform using audiowaveform...`);
    
    const {
        width = 1800,
        height = 280,
        zoom = 256,
        format = 'json'
    } = options;

    try {
        const command = [
            'audiowaveform',
            '-i', `"${inputFile}"`,
            '-o', `"${outputFile}"`,
            '--width', width.toString(),
            '--height', height.toString(),
            '--zoom', zoom.toString()
        ];

        if (format === 'json') {
            command.push('--output-format', 'json');
        } else if (format === 'dat') {
            command.push('--output-format', 'dat');
        }

        const commandString = command.join(' ');
        console.log(`[INFO] Executing: ${commandString}`);

        const { stdout, stderr } = await execAsync(commandString);
        
        if (stderr) {
            console.log(`[WARN] audiowaveform stderr: ${stderr}`);
        }
        
        if (stdout) {
            console.log(`[INFO] audiowaveform stdout: ${stdout}`);
        }

        console.log(`[SUCCESS] Waveform generated successfully: ${outputFile}`);
        return outputFile;

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform: ${error.message}`);
        throw error;
    }
}

async function generateWaveformPeaksFromAudiowaveform(inputFile, outputFile, numPeaks = 32) {
    console.log(`[INFO] Generating waveform peaks using audiowaveform...`);
    
    try {
        const tempJsonFile = `/tmp/temp_waveform_${Date.now()}.json`;
        
        await generateAudioWaveform(inputFile, tempJsonFile, {
            width: numPeaks,
            format: 'json'
        });

        const jsonData = JSON.parse(fs.readFileSync(tempJsonFile, 'utf8'));
        
        if (!jsonData.data || !Array.isArray(jsonData.data)) {
            throw new Error('Invalid waveform JSON format');
        }

        console.log(`[DEBUG] Audiowaveform JSON structure:`);
        console.log(`  - version: ${jsonData.version}`);
        console.log(`  - channels: ${jsonData.channels}`);
        console.log(`  - sample_rate: ${jsonData.sample_rate}`);
        console.log(`  - bits: ${jsonData.bits}`);
        console.log(`  - data length: ${jsonData.data.length}`);
        console.log(`  - first 10 values: ${jsonData.data.slice(0, 10)}`);
        
        const waveformData = jsonData.data;
        const mergedPeaks = [];
        
        if (waveformData.length === numPeaks * 2) {
            console.log(`[DEBUG] Using min/max pairs format`);
            for (let i = 0; i < waveformData.length; i += 2) {
                const min = waveformData[i] || 0;
                const max = waveformData[i + 1] || 0;
                
                const normalizedMin = min / (Math.pow(2, jsonData.bits - 1));
                const normalizedMax = max / (Math.pow(2, jsonData.bits - 1));
                
                mergedPeaks.push(normalizedMin);
                mergedPeaks.push(normalizedMax);
            }
        } else {
            console.log(`[DEBUG] Using alternative format, creating min/max from single values`);
            const samplesPerPeak = Math.floor(waveformData.length / numPeaks);
            
            for (let i = 0; i < numPeaks; i++) {
                const startIdx = i * samplesPerPeak;
                const endIdx = Math.min(startIdx + samplesPerPeak, waveformData.length);
                
                let min = Infinity;
                let max = -Infinity;
                
                for (let j = startIdx; j < endIdx; j++) {
                    const value = waveformData[j] / (Math.pow(2, jsonData.bits - 1));
                    min = Math.min(min, value);
                    max = Math.max(max, value);
                }
                
                mergedPeaks.push(min === Infinity ? 0 : min);
                mergedPeaks.push(max === -Infinity ? 0 : max);
            }
        }

        const peaksString = mergedPeaks.map(peak => {
            const formattedPeak = Math.round(peak * Math.pow(10, 8)) / Math.pow(10, 8);
            return formattedPeak.toString();
        }).join(' ');

        console.log(`[DEBUG] Generated ${mergedPeaks.length} peak values (${mergedPeaks.length/2} peaks)`);

        fs.writeFileSync(outputFile, peaksString);

        if (fs.existsSync(tempJsonFile)) {
            fs.unlinkSync(tempJsonFile);
        }

        console.log(`[SUCCESS] Waveform peaks generated successfully using audiowaveform`);
        return { peaksString, mergedPeaks };

    } catch (error) {
        console.error(`[ERROR] Failed to generate waveform peaks: ${error.message}`);
        throw error;
    }
}

async function createTestWavFile() {
    console.log('[TEST] Creating test WAV file...');
    
    const testAudioFile = '/tmp/test_comparison.wav';
    const sampleRate = 44100;
    const duration = 3; // 3 seconds
    const samples = sampleRate * duration;
    
    // Tạo WAV header
    const wavHeader = Buffer.alloc(44);
    wavHeader.write('RIFF', 0);
    wavHeader.writeUInt32LE(36 + samples * 2, 4);
    wavHeader.write('WAVE', 8);
    wavHeader.write('fmt ', 12);
    wavHeader.writeUInt32LE(16, 16);
    wavHeader.writeUInt16LE(1, 20);
    wavHeader.writeUInt16LE(1, 22);
    wavHeader.writeUInt32LE(sampleRate, 24);
    wavHeader.writeUInt32LE(sampleRate * 2, 28);
    wavHeader.writeUInt16LE(2, 32);
    wavHeader.writeUInt16LE(16, 34);
    wavHeader.write('data', 36);
    wavHeader.writeUInt32LE(samples * 2, 40);
    
    // Tạo complex audio data để có peaks tương tự expected
    const audioData = Buffer.alloc(samples * 2);
    for (let i = 0; i < samples; i++) {
        const t = i / sampleRate;
        // Tạo complex waveform với multiple frequencies và amplitude modulation
        const freq1 = 440 * Math.sin(2 * Math.PI * 0.1 * t);
        const freq2 = 880 * Math.cos(2 * Math.PI * 0.05 * t);
        const amplitude = 0.3 + 0.7 * Math.sin(2 * Math.PI * 0.2 * t);
        
        const sample = amplitude * (
            0.6 * Math.sin(2 * Math.PI * freq1 * t) +
            0.4 * Math.sin(2 * Math.PI * freq2 * t)
        ) * 32767;
        
        audioData.writeInt16LE(Math.round(sample), i * 2);
    }
    
    fs.writeFileSync(testAudioFile, Buffer.concat([wavHeader, audioData]));
    console.log(`[SUCCESS] Test WAV file created: ${testAudioFile}`);
    
    return testAudioFile;
}

async function testPeaksComparison() {
    console.log('=== Testing Peaks Comparison ===\n');
    
    try {
        // Tạo test file
        const testFile = await createTestWavFile();
        
        // Test với số peaks giống expected data
        const numPeaks = expectedPeaks.length / 2;
        console.log(`[TEST] Testing with ${numPeaks} peaks (${expectedPeaks.length} values)`);
        
        const outputFile = '/tmp/test_peaks_output.txt';
        const result = await generateWaveformPeaksFromAudiowaveform(testFile, outputFile, numPeaks);
        
        // So sánh với expected
        console.log(`\n[COMPARISON] Expected vs Generated:`);
        console.log(`Expected peaks: ${expectedPeaks.length} values`);
        console.log(`Generated peaks: ${result.mergedPeaks.length} values`);
        
        console.log(`\nFirst 10 expected values: ${expectedPeaks.slice(0, 10)}`);
        console.log(`First 10 generated values: ${result.mergedPeaks.slice(0, 10)}`);
        
        // Phân tích pattern
        console.log(`\n[ANALYSIS] Pattern analysis:`);
        console.log(`Expected range: ${Math.min(...expectedPeaks)} to ${Math.max(...expectedPeaks)}`);
        console.log(`Generated range: ${Math.min(...result.mergedPeaks)} to ${Math.max(...result.mergedPeaks)}`);
        
        // Cleanup
        [testFile, outputFile].forEach(file => {
            if (fs.existsSync(file)) {
                fs.unlinkSync(file);
            }
        });
        
        console.log('\n=== Test Complete ===');
        
    } catch (error) {
        console.error(`[ERROR] Test failed: ${error.message}`);
    }
}

if (require.main === module) {
    testPeaksComparison();
}

module.exports = { testPeaksComparison };
