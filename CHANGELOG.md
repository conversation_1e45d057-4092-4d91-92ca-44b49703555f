# Changelog

## [2.0.0] - 2025-07-28

### 🚀 Major Changes

#### Direct Audio Processing
- **NEW**: Audio files được xử lý trực tiếp, không cần chuyển đổi sang MP3
- **NEW**: Generate mergedPeaks ngay lập tức từ file gốc (WAV, MP3, M4A, AAC, FLAC, OGG)
- **NEW**: G<PERSON><PERSON>m thời gian xử lý từ ~30 giây xuống ~5 giây cho audio files
- **NEW**: Tiết kiệm storage (không tạo file MP3 trung gian)
- **NEW**: MergedPeaks format (min-max pairs) thay vì Peaks (max only)
- **IMPROVED**: <PERSON><PERSON> sang WAV tạm thời để xử lý ổn định
- **IMPROVED**: Sử dụng FFmpeg WAV conversion để extract audio samples hiệu quả hơn
- **FIXED**: FFmpeg pipe error trong Lambda environment
- **FIXED**: WAV header parsing (44 bytes) để extract samples ch<PERSON>h xác

#### Architecture Changes
- **MODIFIED**: `startConversionHandler` gi<PERSON> xử lý audio trực tiếp
- **MODIFIED**: Video files vẫn dùng MediaConvert như cũ
- **REMOVED**: `generatePeaksHandler` - Không còn cần thiết

#### Output Files
- **CHANGED**: Audio files: `folder/file_peaks.txt` (thay vì `folder/file-converted_peaks.txt`)
- **REMOVED**: Audio files không còn tạo `folder/file-converted.mp3`
- **NEW**: MergedPeaks format: `[-0.3, 0.5, -0.2, 0.8, ...]` thay vì `[0.5, 0.8, ...]`

### 🔧 Technical Improvements

#### Docker Optimization
- **NEW**: Multi-stage build giảm image size từ 1.81GB → 1.19GB (34% reduction)
- **NEW**: Cleanup build tools và cache trong Docker layers
- **NEW**: Optimized dependencies và temporary files cleanup

#### Code Quality
- **IMPROVED**: Error handling cho direct audio processing
- **IMPROVED**: Database transaction management
- **IMPROVED**: S3 operations với proper cleanup

### 📦 Dependencies

#### Removed
- `audiowaveform` - Không còn cần thiết
- `wavesurfer.js` - Chỉ dùng FFmpeg
- `web-audio-api` - Chỉ dùng FFmpeg
- `node-fetch` - Không cần thiết

#### Kept
- `aws-sdk` - AWS services
- `mysql2` - Database connectivity  
- `fluent-ffmpeg` - Audio processing

### 🎯 Performance

#### Audio Files Processing
- **BEFORE**: Upload → MediaConvert → MP3 → Generate Peaks (~30s)
- **AFTER**: Upload → Generate Peaks Directly (~5s)

#### Storage Usage
- **BEFORE**: Original + MP3 + Peaks (3 files)
- **AFTER**: Original + Peaks (2 files)

### 🔄 Migration Guide

#### Frontend Changes
```javascript
// OLD
fetch('/path/to/file-converted_peaks.txt')
// peaks format: [0.5, 0.8, 0.3, ...]

// NEW  
fetch('/path/to/file_peaks.txt')
// mergedPeaks format: [-0.3, 0.5, -0.2, 0.8, ...]
```

#### Database Schema
- Không thay đổi schema
- `waveform_data_key` giờ chứa `file_peaks.txt` thay vì `file-converted_peaks.txt`

#### Wavesurfer Integration
- **CHANGED**: Sử dụng `wavesurfer.backend.setMergedPeaks()` thay vì `setPeaks()`
- **CHANGED**: Data format từ 32 values thành 64 values (min-max pairs)
- **IMPROVED**: Waveform visualization chi tiết hơn với negative amplitudes
- **NEW**: Hỗ trợ truy cập trực tiếp `wavesurfer.backend.mergedPeaks`

#### AWS Lambda Configuration
- **REMOVED**: S3 Output Bucket trigger cho `generatePeaksHandler`
- **KEPT**: S3 Input Bucket trigger cho `startConversionHandler`
- **KEPT**: EventBridge trigger cho `handleCompletionHandler`

### 🧪 Testing

#### New Test Cases
- Direct audio processing
- Video MediaConvert flow
- Error handling
- Database updates

### 📚 Documentation

#### Updated
- README.md với logic mới
- Architecture diagrams
- API documentation
- Deployment guide

### 🚨 Breaking Changes

1. **Audio file peaks path changed**: `file-converted_peaks.txt` → `file_peaks.txt`
2. **No MP3 conversion for audio files**: Audio files không tạo MP3 nữa
3. **Faster processing**: Audio files xử lý nhanh hơn nhiều
4. **Removed handler**: `generatePeaksHandler` không còn tồn tại
5. **Simplified triggers**: Chỉ cần 2 triggers thay vì 3
6. **MergedPeaks format**: Data format thay đổi từ peaks sang mergedPeaks
7. **Frontend API change**: `setPeaks()` → `setMergedPeaks()`

### ✅ Compatibility

- **Wavesurfer.js**: 100% compatible với mergedPeaks
- **Database**: Backward compatible
- **S3 Triggers**: Không thay đổi
- **EventBridge**: Không thay đổi cho video files
- **Frontend**: Cần update từ `setPeaks()` sang `setMergedPeaks()` 