import os
import json
import boto3
import pymysql
import subprocess
import numpy as np
from scipy.io import wavfile
from datetime import datetime
from urllib.parse import unquote_plus

# --- C<PERSON>u hình ---
# <PERSON><PERSON><PERSON> biến môi trường sẽ được <PERSON> inject vào
OUTPUT_BUCKET = os.environ['OUTPUT_BUCKET']
MEDIA_CONVERT_ROLE_ARN = os.environ['MEDIA_CONVERT_ROLE_ARN']
MEDIA_CONVERT_ENDPOINT = os.environ['MEDIA_CONVERT_ENDPOINT']
RDS_PARAM_NAME = os.environ['RDS_PARAM_NAME']

# Danh sách các định dạng file được hỗ trợ
AUDIO_EXTENSIONS = ['.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg']
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.mkv', '.avi', '.webm', '.mpg']

# Khởi tạo client
s3_client = boto3.client('s3')
mc_client = boto3.client('mediaconvert', endpoint_url=MEDIA_CONVERT_ENDPOINT)
ssm_client = boto3.client('ssm')
db_connection = None


def get_database_credentials():
    response = ssm_client.get_parameter(
        Name=RDS_PARAM_NAME,
        WithDecryption=True
    )
    return json.loads(response['Parameter']['Value'])

def generate_wavesurfer_peaks(input_file, output_file, num_peaks=32, method='max'):
    """
    Tạo peaks data theo logic của wavesurfer với số lượng peaks được chỉ định.
    
    Args:
        input_file: Đường dẫn file audio đầu vào
        output_file: Đường dẫn file output
        num_peaks: Số lượng peaks cần tạo
        method: Phương pháp tính peaks ('max', 'rms', 'both')
    """
    try:
        print(f"[INFO] Loading audio file: {input_file}")
        
        # Check if file is WAV, if not convert to WAV using ffmpeg
        temp_wav = None
        if not input_file.lower().endswith('.wav'):
            temp_wav = f"/tmp/temp_audio_{os.getpid()}.wav"
            print(f"[INFO] Converting {input_file} to WAV format...")
            subprocess.run([
                'ffmpeg', '-i', input_file, '-ac', '1', '-ar', '44100', 
                '-acodec', 'pcm_s16le', temp_wav, '-y'
            ], check=True, stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
            audio_file = temp_wav
        else:
            audio_file = input_file
        
        # Load audio file using scipy.io.wavfile
        sample_rate, samples = wavfile.read(audio_file)
        
        # Clean up temp file if created
        if temp_wav and os.path.exists(temp_wav):
            os.remove(temp_wav)
        
        # Convert to mono if stereo
        if len(samples.shape) > 1:
            samples = np.mean(samples, axis=1)
        
        # Normalize to -1.0 to 1.0 range based on data type
        if samples.dtype == np.int16:
            samples = samples.astype(np.float32) / 32768.0
        elif samples.dtype == np.int32:
            samples = samples.astype(np.float32) / 2147483648.0
        elif samples.dtype == np.int8:
            samples = samples.astype(np.float32) / 128.0
        elif samples.dtype in [np.float32, np.float64]:
            # Already normalized
            samples = samples.astype(np.float32)
        
        duration_ms = len(samples) * 1000 / sample_rate
        print(f"[INFO] Audio loaded - Duration: {duration_ms:.1f}ms, Samples: {len(samples)}, Method: {method}")
        
        # Chia audio thành num_peaks phần và tính amplitude cho mỗi phần
        samples_per_peak = len(samples) // num_peaks
        peaks = []
        
        for i in range(num_peaks):
            start_idx = i * samples_per_peak
            end_idx = start_idx + samples_per_peak
            if i == num_peaks - 1:  # Phần cuối cùng lấy hết samples còn lại
                end_idx = len(samples)
            
            # Tính amplitude value trong segment này
            segment = samples[start_idx:end_idx]
            if len(segment) > 0:
                # Wavesurfer chủ yếu sử dụng max absolute value
                if method == 'max':
                    # Max absolute value - giống wavesurfer
                    peak_value = np.max(np.abs(segment))
                elif method == 'rms':
                    # RMS (Root Mean Square)
                    peak_value = np.sqrt(np.mean(segment**2))
                elif method == 'both':
                    # Trung bình của max và RMS
                    max_val = np.max(np.abs(segment))
                    rms_val = np.sqrt(np.mean(segment**2))
                    peak_value = (max_val + rms_val) / 2.0
                else:
                    peak_value = np.max(np.abs(segment))
            else:
                peak_value = 0.0
            
            peaks.append(peak_value)
        
        print(f"[INFO] Generated {len(peaks)} peaks using {method} method")
        
        # Peaks đã được normalize trong khoảng 0-1 rồi (do samples đã normalize)
        # Format peaks theo logic của wavesurfer
        # Math.round(peaks[i] * Math.pow(10, 8)) / Math.pow(10, 8)
        peaks_string = ""
        for peak in peaks:
            # Ensure peak is between 0-1
            normalized_peak = max(0.0, min(1.0, peak))
            formatted_peak = round(normalized_peak * (10 ** 8)) / (10 ** 8)
            peaks_string += str(formatted_peak) + " "
        
        # Remove trailing space
        peaks_string = peaks_string.strip()
        
        print(f"[INFO] Writing peaks to file: {output_file}")
        with open(output_file, 'w') as f:
            f.write(peaks_string)
        
        print(f"[SUCCESS] Wavesurfer peaks generated successfully")
        
    except Exception as e:
        print(f"[ERROR] Failed to generate wavesurfer peaks: {e}")
        # Clean up temp file if exists
        if 'temp_wav' in locals() and temp_wav and os.path.exists(temp_wav):
            os.remove(temp_wav)
        raise e

def audiowaveform_to_wavesurfer_peaks(audiowaveform_json_file, output_file, num_peaks=32):
    """
    Convert audiowaveform JSON output thành format tương thích với wavesurfer.
    
    Args:
        audiowaveform_json_file: Đường dẫn file JSON từ audiowaveform
        output_file: Đường dẫn file output cho wavesurfer
        num_peaks: Số lượng peaks cần tạo (mặc định 32)
    """
    try:
        print(f"[INFO] Converting audiowaveform JSON to wavesurfer format...")
        
        # Load audiowaveform JSON data
        with open(audiowaveform_json_file, 'r') as f:
            waveform_data = json.load(f)
        
        # Extract data array từ audiowaveform
        data = waveform_data.get('data', [])
        if not data:
            raise ValueError("No data found in audiowaveform JSON")
        
        print(f"[INFO] Audiowaveform data points: {len(data)}")
        
        # Audiowaveform data có format [min, max, min, max, ...]
        # Chúng ta sẽ lấy max values để tạo peaks
        max_values = []
        for i in range(0, len(data), 2):
            if i + 1 < len(data):
                # Lấy max value (absolute value của min và max)
                min_val = abs(data[i])
                max_val = abs(data[i + 1]) if i + 1 < len(data) else 0
                max_values.append(max(min_val, max_val))
        
        print(f"[INFO] Extracted {len(max_values)} max values from audiowaveform")
        
        # Downsample về num_peaks nếu cần
        if len(max_values) > num_peaks:
            # Chia thành num_peaks segments và lấy max của mỗi segment
            segment_size = len(max_values) // num_peaks
            peaks = []
            
            for i in range(num_peaks):
                start_idx = i * segment_size
                end_idx = start_idx + segment_size
                if i == num_peaks - 1:  # Segment cuối lấy hết
                    end_idx = len(max_values)
                
                segment = max_values[start_idx:end_idx]
                if segment:
                    # Lấy max value trong segment
                    peak_value = max(segment)
                else:
                    peak_value = 0.0
                
                peaks.append(peak_value)
        
        elif len(max_values) < num_peaks:
            # Upsample bằng interpolation đơn giản
            peaks = []
            for i in range(num_peaks):
                # Linear interpolation
                float_index = (i * (len(max_values) - 1)) / (num_peaks - 1)
                lower_index = int(float_index)
                upper_index = min(lower_index + 1, len(max_values) - 1)
                
                if lower_index == upper_index:
                    peak_value = max_values[lower_index]
                else:
                    # Linear interpolation
                    weight = float_index - lower_index
                    peak_value = max_values[lower_index] * (1 - weight) + max_values[upper_index] * weight
                
                peaks.append(peak_value)
        else:
            # Đúng số lượng peaks rồi
            peaks = max_values
        
        print(f"[INFO] Generated {len(peaks)} peaks for wavesurfer")
        
        # Normalize peaks về scale 0-1 dựa trên bits
        bits = waveform_data.get('bits', 8)
        max_value = (2 ** bits) - 1
        normalized_peaks = [peak / max_value for peak in peaks]
        
        # Format peaks theo logic của wavesurfer
        # Math.round(peaks[i] * Math.pow(10, 8)) / Math.pow(10, 8)
        peaks_string = ""
        for peak in normalized_peaks:
            formatted_peak = round(peak * (10 ** 8)) / (10 ** 8)
            peaks_string += str(formatted_peak) + " "
        
        # Remove trailing space
        peaks_string = peaks_string.strip()
        
        print(f"[INFO] Writing wavesurfer peaks to file: {output_file}")
        with open(output_file, 'w') as f:
            f.write(peaks_string)
        
        print(f"[SUCCESS] Audiowaveform converted to wavesurfer format successfully")
        
    except Exception as e:
        print(f"[ERROR] Failed to convert audiowaveform to wavesurfer: {e}")
        raise e


def get_db_connection():
    global db_connection
    credentials = get_database_credentials()
    if db_connection and db_connection.open: 
        return db_connection
    conn = pymysql.connect(
        host=credentials['db_host'], 
        port=credentials['db_port'], 
        user=credentials['db_username'], 
        password=credentials['db_password'], 
        db=credentials['db_name'],
        autocommit=False
    )
    db_connection = conn
    return conn


# =================================================================
# HANDLER 1: Bắt đầu Job (Được trigger bởi S3 bucket đầu vào)
# =================================================================
def start_conversion_handler(event, context):
    """
    Trigger bởi file upload lên S3, tạo bản ghi trong DB và khởi động job MediaConvert.
    """
    record = event['Records'][0]
    source_bucket = record['s3']['bucket']['name']
    source_key = unquote_plus(record['s3']['object']['key'])

    print(f"[START] Processing file: s3://{source_bucket}/{source_key}")

    # --- SỬA ĐỔI LOGIC TẠI ĐÂY ---

    # 1. Lấy đường dẫn cơ sở không có phần mở rộng
    # Ví dụ: 'test_folder/A.wav' -> 'test_folder/A'
    base_path_without_ext = os.path.splitext(source_key)[0]

    # 2. Tạo thư mục đích trên S3 output, đây chính là thư mục con mới
    # Ví dụ: 'test_folder/A' -> 's3://[OUTPUT_BUCKET]/test_folder/A/'
    destination_folder = f"s3://{OUTPUT_BUCKET}/{base_path_without_ext}/"

    print(f"[INFO] Destination folder: {destination_folder}")

    # --- KẾT THÚC SỬA ĐỔI ---

    # 3. Phát hiện loại file
    _, extension = os.path.splitext(source_key)
    file_type = 'unknown'
    if extension.lower() in VIDEO_EXTENSIONS:
        file_type = 'video'
    elif extension.lower() in AUDIO_EXTENSIONS:
        file_type = 'audio'

    print(f"[INFO] File type detected: {file_type} (extension: {extension})")

    if file_type == 'unknown':
        print(f"[ERROR] Unsupported file type: {extension}. Skipping.")
        return {'statusCode': 200, 'body': 'Unsupported file type'}

    # 3.2. Tạo record trong database và tạo job MediaConvert
    now = datetime.now()
    
    # 4. Xây dựng cấu hình job MediaConvert
    input_settings = {
        "AudioSelectors": {"Audio Selector 1": {"DefaultSelection": "DEFAULT"}},
        "FileInput": f"s3://{source_bucket}/{source_key}"
    }

    # NameModifier chỉ là hậu tố, không cần chứa tên file
    name_modifier = "-converted"

    hls_output_group = {
        "Name": "Apple HLS",
        "Outputs": [{"Preset": "System-Avc_16x9_720p_29_97fps_3500kbps", "NameModifier": name_modifier}],
        "OutputGroupSettings": {
            "Type": "HLS_GROUP_SETTINGS",
            "HlsGroupSettings": {"SegmentLength": 10, "Destination": destination_folder, "MinSegmentLength": 0}
        }
    }

    mp3_output_group = {
        "Name": "File Group",
        "Outputs": [
            {
                "ContainerSettings": {"Container": "RAW"},
                "AudioDescriptions": [{
                    "AudioSourceName": "Audio Selector 1",
                    "CodecSettings": {
                        "Codec": "AAC",
                        "AacSettings": {"Bitrate": 96000, "CodingMode": "CODING_MODE_2_0", "SampleRate": 48000}
                    }
                }],
                "Extension": "mp3",
                "NameModifier": name_modifier
            }
        ],
        "OutputGroupSettings": {
            "Type": "FILE_GROUP_SETTINGS",
            "FileGroupSettings": {"Destination": destination_folder}
        }
    }

    job_settings = {"Inputs": [input_settings]}
    if file_type == 'video':
        job_settings['OutputGroups'] = [hls_output_group]
    else:  # audio
        job_settings['OutputGroups'] = [mp3_output_group]

    # 5. Tạo job MediaConvert và insert/update database trong 1 lần kết nối
    conn = get_db_connection()
    try:
        print(f"[INFO] Creating MediaConvert job for {file_type} file...")
        # Tạo MediaConvert job trước
        response = mc_client.create_job(
            Role=MEDIA_CONVERT_ROLE_ARN,
            Settings=job_settings,
            UserMetadata={'original_object_key': source_key}
        )
        job_id = response['Job']['Id']
        print(f"[SUCCESS] MediaConvert job created with ID: {job_id}")
        
        # Insert vào database với job_id luôn (chỉ 1 truy vấn)
        print(f"[INFO] Upserting record into database...")
        with conn.cursor() as cur:
            # Kiểm tra xem record đã tồn tại chưa
            cur.execute(
                """SELECT id, status FROM app_mediaconvertjob 
                   WHERE original_object_key = %s""",
                (source_key,)
            )
            existing_record = cur.fetchone()
            
            if existing_record:
                record_id, current_status = existing_record
                print(f"[INFO] Record already exists (ID: {record_id}, Status: {current_status})")
                
                # Chỉ update nếu status hiện tại không phải COMPLETE
                if current_status not in ['COMPLETE', 'PROCESSING']:
                    cur.execute(
                        """UPDATE app_mediaconvertjob 
                           SET status = %s, 
                               job_id = %s,
                               error_message = NULL,
                               modified = %s
                           WHERE original_object_key = %s""",
                        ('PROCESSING', job_id, now, source_key)
                    )
                    print(f"[SUCCESS] Updated existing record - Status: PROCESSING, Job ID: {job_id}")
                else:
                    print(f"[INFO] Record already in final state ({current_status}), skipping update")
            else:
                # Insert mới nếu chưa tồn tại
                cur.execute(
                    """INSERT INTO app_mediaconvertjob 
                       (original_object_key, status, job_id, created, modified)
                       VALUES (%s, %s, %s, %s, %s)""",
                    (source_key, 'PROCESSING', job_id, now, now)
                )
                print(f"[SUCCESS] New database record created - Status: PROCESSING, Job ID: {job_id}")
        conn.commit()
        
    except Exception as e:
        print(f"[ERROR] Failed to create MediaConvert job: {e}")
        # Chỉ insert record FAILED nếu tạo job thất bại (1 truy vấn)
        try:
            print(f"[INFO] Upserting FAILED record into database...")
            with conn.cursor() as cur:
                # Kiểm tra xem record đã tồn tại chưa
                cur.execute(
                    """SELECT id, status FROM app_mediaconvertjob 
                       WHERE original_object_key = %s""",
                    (source_key,)
                )
                existing_record = cur.fetchone()
                
                if existing_record:
                    record_id, current_status = existing_record
                    print(f"[INFO] Record already exists (ID: {record_id}, Status: {current_status})")
                    
                    # Update status thành FAILED
                    cur.execute(
                        """UPDATE app_mediaconvertjob 
                           SET status = 'FAILED',
                               error_message = %s,
                               modified = %s
                           WHERE original_object_key = %s""",
                        (str(e), now, source_key)
                    )
                    print(f"[SUCCESS] Updated existing record - Status: FAILED")
                else:
                    # Insert mới nếu chưa tồn tại
                    cur.execute(
                        """INSERT INTO app_mediaconvertjob 
                           (original_object_key, status, error_message, created, modified)
                           VALUES (%s, %s, %s, %s, %s)""",
                        (source_key, 'FAILED', str(e), now, now)
                    )
                    print(f"[SUCCESS] New FAILED record created")
            conn.commit()
        except Exception as db_error:
            print(f"[ERROR] Failed to insert into database: {db_error}")
            conn.rollback()
    finally:
        conn.close()

    print(f"[END] Completed processing for: {source_key}")
    return {'statusCode': 200, 'body': 'Job created successfully'}


# HANDLER 2: Xử lý kết quả (Được trigger bởi EventBridge)
# =================================================================
def handle_completion_handler(event, context):
    """
    Trigger bởi EventBridge khi job MediaConvert hoàn thành (thành công hoặc thất bại).
    """
    job_status = event['detail']['status']
    user_metadata = event['detail']['userMetadata']
    original_key = user_metadata['original_object_key']

    print(f"[START] Job completion handler - File: {original_key}, Status: {job_status}")

    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            if job_status == 'COMPLETE':
                print(f"[INFO] Processing COMPLETE status for: {original_key}")
                # Lấy đường dẫn file output từ chi tiết job
                output_details = event['detail']['outputGroupDetails'][0]['outputDetails'][0]
                output_key_full = output_details['outputFilePaths'][0]  # s3://bucket/A/A-converted.m3u8
                
                # Chỉ lấy phần key, bỏ "s3://bucket/"
                output_key = "/".join(output_key_full.split('/')[3:])
                print(f"[INFO] Output file key: {output_key}")
                
                # Xác định loại file để quyết định bước tiếp theo
                is_audio = output_key.lower().endswith('.mp3')
                next_status = 'GENERATING_PEAKS' if is_audio else 'COMPLETE'
                print(f"[INFO] Next status: {next_status} (is_audio: {is_audio})")
                
                cur.execute(
                    """UPDATE app_mediaconvertjob 
                       SET status = %s,
                           converted_media_key = %s,
                           modified = %s
                       WHERE original_object_key = %s;""",
                    (next_status, output_key, datetime.now(), original_key)
                )
                print(f"[SUCCESS] Database updated - Status: {next_status}, Output: {output_key}")
                
            elif job_status == 'ERROR':
                print(f"[INFO] Processing ERROR status for: {original_key}")
                error_message = event['detail'].get('errorMessage', 'Unknown error')
                print(f"[ERROR] Job error message: {error_message}")
                cur.execute(
                    """UPDATE app_mediaconvertjob 
                       SET status = 'FAILED',
                           error_message = %s,
                           modified = %s
                       WHERE original_object_key = %s;""",
                    (error_message, datetime.now(), original_key)
                )
                print(f"[SUCCESS] Database updated - Status: FAILED")
        conn.commit()
    except Exception as e:
        print(f"[ERROR] Failed to update database: {e}")
        conn.rollback()
    finally:
        conn.close()

    print(f"[END] Completed job completion handler for: {original_key}")
    return {'statusCode': 200, 'body': 'Database updated'}

# =================================================================
# HANDLER 3: Tạo sóng nhạc (Được trigger bởi S3 bucket đầu ra)
# =================================================================
def generate_peaks_handler(event, context):
    """
    Trigger bởi file .mp3 được tạo ra, tạo file peak data và cập nhật DB.
    """
    record = event['Records'][0]
    # source_key sẽ có dạng: "A/A-converted.mp3"
    source_key = unquote_plus(record['s3']['object']['key'])

    print(f"[START] Peak generation handler - File: {source_key}")

    # Chỉ xử lý file .mp3
    if not source_key.lower().endswith('.mp3'):
        print(f"[INFO] Not an MP3 file ({source_key}), skipping.")
        return {'statusCode': 200, 'body': 'Not an MP3 file, skipping.'}

    # 1. Xác định các đường dẫn
    base_s3_path = os.path.splitext(source_key)[0]
    peaks_s3_key = f"{base_s3_path}.json" # -> "A/A-converted.json"
    wavesurfer_peaks_s3_key = f"{base_s3_path}_peaks.txt" # -> "A/A-converted_peaks.txt"

    print(f"[INFO] Waveform output key: {peaks_s3_key}")
    print(f"[INFO] Wavesurfer peaks output key: {wavesurfer_peaks_s3_key}")

    local_input = f"/tmp/{os.path.basename(source_key)}"
    local_output = f"/tmp/{os.path.basename(peaks_s3_key)}"
    local_peaks_output = f"/tmp/{os.path.basename(wavesurfer_peaks_s3_key)}"

    print(f"[INFO] Local paths - Input: {local_input}, Waveform: {local_output}, Peaks: {local_peaks_output}")

    # 2. Tạo file peak data
    status = 'COMPLETE'
    error_message = None
    waveform_key = None
    
    try:
        print(f"[INFO] Downloading MP3 file from S3...")
        s3_client.download_file(OUTPUT_BUCKET, source_key, local_input)
        
        # Sử dụng logic tự viết để đảm bảo 100% tương thích với wavesurfer
        print(f"[INFO] Generating wavesurfer-compatible peaks data...")
        generate_wavesurfer_peaks(local_input, local_peaks_output, method='max')
        
        # Tạo audiowaveform data cho detailed visualization (tùy chọn)
        print(f"[INFO] Generating detailed waveform data using audiowaveform...")
        subprocess.run(
            ['audiowaveform', '-i', local_input, '-o', local_output, '--pixels-per-second', '50', '--bits', '8'],
            check=True, stderr=subprocess.DEVNULL
        )
        
        print(f"[INFO] Uploading waveform data to S3...")
        s3_client.upload_file(local_output, OUTPUT_BUCKET, peaks_s3_key, ExtraArgs={'ContentType': 'application/json'})
        
        print(f"[INFO] Uploading wavesurfer peaks data to S3...")
        s3_client.upload_file(local_peaks_output, OUTPUT_BUCKET, wavesurfer_peaks_s3_key, ExtraArgs={'ContentType': 'text/plain'})
        
        # Cleanup temporary files
        os.remove(local_input)
        os.remove(local_output)
        os.remove(local_peaks_output)
        print(f"[INFO] Cleaned up temporary files")
        
        # Sử dụng wavesurfer peaks file làm waveform_key
        waveform_key = wavesurfer_peaks_s3_key
        print(f"[SUCCESS] Waveform generation completed: {waveform_key}")
        
    except Exception as e:
        print(f"[ERROR] Failed to generate peak data for {source_key}: {e}")
        status = 'FAILED'
        error_message = f"Peak generation failed: {e}"
        
        # Cleanup nếu có file tạm
        try:
            if os.path.exists(local_input):
                os.remove(local_input)
                print(f"[INFO] Cleaned up temporary input file")
            if os.path.exists(local_output):
                os.remove(local_output)
                print(f"[INFO] Cleaned up temporary output file")
            if os.path.exists(local_peaks_output):
                os.remove(local_peaks_output)
                print(f"[INFO] Cleaned up temporary peaks file")
        except:
            pass

    # 3. Cập nhật DB một lần duy nhất
    print(f"[INFO] Updating database with status: {status}")
    print(f"[DEBUG] Looking for record with converted_media_key: {source_key}")
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            if status == 'COMPLETE':
                cur.execute(
                    """UPDATE app_mediaconvertjob 
                       SET status = %s, 
                           waveform_data_key = %s,
                           modified = %s
                       WHERE converted_media_key = %s;""",
                    (status, waveform_key, datetime.now(), source_key)
                )
                rows_affected = cur.rowcount
                print(f"[DEBUG] Rows affected by UPDATE: {rows_affected}")
                if rows_affected == 0:
                    print(f"[WARNING] No record found with converted_media_key: {source_key}")
                    # Thử tìm record để debug
                    cur.execute(
                        """SELECT id, original_object_key, converted_media_key, status 
                           FROM app_mediaconvertjob 
                           WHERE converted_media_key LIKE %s OR original_object_key LIKE %s""",
                        (f"%{os.path.basename(source_key)}%", f"%{os.path.basename(source_key)}%")
                    )
                    records = cur.fetchall()
                    print(f"[DEBUG] Similar records found: {records}")
                    
                    # Thử update bằng cách khác - tìm record có status = 'GENERATING_PEAKS'
                    # và original_object_key chứa base name tương tự
                    base_name = os.path.splitext(os.path.basename(source_key))[0].replace('-converted', '')
                    print(f"[DEBUG] Trying backup update with base_name: {base_name}")
                    cur.execute(
                        """UPDATE app_mediaconvertjob 
                           SET status = %s, 
                               waveform_data_key = %s,
                               converted_media_key = %s,
                               modified = %s
                           WHERE status = 'GENERATING_PEAKS' 
                           AND original_object_key LIKE %s;""",
                        (status, waveform_key, source_key, datetime.now(), f"%{base_name}%")
                    )
                    backup_rows = cur.rowcount
                    print(f"[DEBUG] Backup update rows affected: {backup_rows}")
                    if backup_rows > 0:
                        print(f"[SUCCESS] Database updated via backup method - Status: {status}, Waveform: {waveform_key}")
                else:
                    print(f"[SUCCESS] Database updated - Status: {status}, Waveform: {waveform_key}")
            else:  # FAILED
                cur.execute(
                    """UPDATE app_mediaconvertjob 
                       SET status = %s, 
                           error_message = %s,
                           modified = %s
                       WHERE converted_media_key = %s;""",
                    (status, error_message, datetime.now(), source_key)
                )
                rows_affected = cur.rowcount
                print(f"[DEBUG] Rows affected by UPDATE: {rows_affected}")
                if rows_affected == 0:
                    print(f"[WARNING] No record found with converted_media_key: {source_key}")
                else:
                    print(f"[SUCCESS] Database updated - Status: {status}, Error: {error_message}")
        conn.commit()
    except Exception as e:
        print(f"[ERROR] Failed to update database: {e}")
        conn.rollback()
    finally:
        conn.close()

    print(f"[END] Completed peak generation handler for: {source_key}")

    if status == 'FAILED':
        return {'statusCode': 500, 'body': 'Peak generation failed'}
    
    return {'statusCode': 200, 'body': 'Peaks generated and database updated'}