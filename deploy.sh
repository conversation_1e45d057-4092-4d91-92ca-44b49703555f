#!/bin/bash

# Deployment script cho Soremo MediaConvert Lamb<PERSON> (Node.js)

set -e

# Configuration
FUNCTION_NAME="soremo-mediaconvert"
REGION="us-east-1"
ECR_REPO="soremo-mediaconvert-lambda"

echo "🚀 Deploying Soremo MediaConvert Lambda (Node.js)..."

# Build Docker image
echo "📦 Building Docker image..."
docker build -t ${ECR_REPO}:latest .

# Tag và push lên ECR (nếu cần)
if [ "$1" = "push" ]; then
    echo "📤 Pushing to ECR..."
    
    # Get ECR login
    aws ecr get-login-password --region ${REGION} | docker login --username AWS --password-stdin $(aws sts get-caller-identity --query Account --output text).dkr.ecr.${REGION}.amazonaws.com
    
    # Tag và push
    docker tag ${ECR_REPO}:latest $(aws sts get-caller-identity --query Account --output text).dkr.ecr.${REGION}.amazonaws.com/${ECR_REPO}:latest
    docker push $(aws sts get-caller-identity --query Account --output text).dkr.ecr.${REGION}.amazonaws.com/${ECR_REPO}:latest
    
    echo "✅ Image pushed to ECR successfully!"
fi

echo "✅ Build completed!"
echo ""
echo "📋 Next steps:"
echo "1. Update Lambda function với image URI mới"
echo "2. Set environment variables:"
echo "   - OUTPUT_BUCKET=your-output-bucket"
echo "   - MEDIA_CONVERT_ROLE_ARN=your-role-arn" 
echo "   - MEDIA_CONVERT_ENDPOINT=your-endpoint"
echo "   - RDS_PARAM_NAME=your-db-param-name"
echo "3. Configure triggers cho 3 handlers:"
echo "   - startConversionHandler: S3 input bucket"
echo "   - handleCompletionHandler: EventBridge MediaConvert"
echo "   - generatePeaksHandler: S3 output bucket (.mp3 files)" 