# Build stage for ffmpeg
FROM public.ecr.aws/lambda/nodejs:18 AS ffmpeg-builder
RUN yum update -y && \
    yum install -y wget xz tar && \
    wget https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz && \
    tar xf ffmpeg-release-amd64-static.tar.xz && \
    mv ffmpeg-*-static/ffmpeg /tmp/ffmpeg && \
    mv ffmpeg-*-static/ffprobe /tmp/ffprobe && \
    chmod +x /tmp/ffmpeg /tmp/ffprobe && \
    rm -rf ffmpeg-* ffmpeg-release-amd64-static.tar.xz && \
    yum remove -y wget xz tar && \
    yum clean all && \
    rm -rf /var/cache/yum

# Final stage - use AWS Lambda base
FROM public.ecr.aws/lambda/nodejs:18

# Copy ffmpeg binaries from builder stage
COPY --from=ffmpeg-builder /tmp/ffmpeg /usr/local/bin/ffmpeg
COPY --from=ffmpeg-builder /tmp/ffprobe /usr/local/bin/ffprobe

# Copy package.json and install dependencies
COPY package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force && \
    rm -rf /tmp/* /var/tmp/*

# Copy application code
COPY index.js ./

# Set the CMD to your handler (could also be done as a parameter override outside of the Dockerfile)
CMD [ "index.startConversionHandler" ]