# Architecture Overview

## 🏗️ Simplified Architecture (v2.0.0)

### Flow Diagram

```
┌─────────────────┐    ┌─────────────────────┐    ┌─────────────────┐
│   S3 Upload     │───▶│  Handler 1          │───▶│   Database      │
│   (Audio/Video) │    │  startConversion    │    │   Update        │
└─────────────────┘    └─────────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Decision      │
                       │   Point         │
                       └─────────────────┘
                                │
                    ┌───────────┴───────────┐
                    │                       │
                    ▼                       ▼
            ┌─────────────┐         ┌─────────────┐
            │   Audio     │         │   Video     │
            │   Direct    │         │ MediaConvert│
            │  Processing │         │    Job      │
            └─────────────┘         └─────────────┘
                    │                       │
                    ▼                       ▼
            ┌─────────────┐         ┌─────────────┐
            │   Peaks     │         │ EventBridge │
            │  Generated  │         │  Trigger    │
            └─────────────┘         └─────────────┘
                    │                       │
                    ▼                       ▼
            ┌─────────────┐         ┌─────────────┐
            │   S3        │         │  Handler 2  │
            │  Upload     │         │ handleCompletion│
            └─────────────┘         └─────────────┘
                    │                       │
                    ▼                       ▼
            ┌─────────────┐         ┌─────────────┐
            │   COMPLETE  │         │   COMPLETE  │
            └─────────────┘         └─────────────┘
```

## 📊 Handler Comparison

### Before (v1.x)
| Handler | Trigger | Purpose | Status |
|---------|---------|---------|--------|
| `startConversionHandler` | S3 Upload | Create MediaConvert job | PROCESSING |
| `handleCompletionHandler` | EventBridge | Update DB after conversion | COMPLETE/GENERATING_PEAKS |
| `generatePeaksHandler` | S3 Output (*.mp3) | Generate peaks from MP3 | COMPLETE |

### After (v2.0.0)
| Handler | Trigger | Purpose | Status |
|---------|---------|---------|--------|
| `startConversionHandler` | S3 Upload | Direct audio processing OR create MediaConvert job | COMPLETE/PROCESSING |
| `handleCompletionHandler` | EventBridge | Update DB after video conversion | COMPLETE |

## 🎯 Processing Flows

### Audio Files Flow
```
1. Upload audio file to S3
2. startConversionHandler triggered
3. Download file from S3
4. Generate peaks directly using FFmpeg
5. Upload peaks to S3
6. Update database: COMPLETE
7. Done! (~5 seconds)
```

### Video Files Flow
```
1. Upload video file to S3
2. startConversionHandler triggered
3. Create MediaConvert job
4. Update database: PROCESSING
5. MediaConvert processes video → HLS
6. EventBridge triggers handleCompletionHandler
7. Update database: COMPLETE
8. Done! (~30-60 seconds)
```

## 🔧 Technical Details

### Handler 1: startConversionHandler
```javascript
// Logic flow
if (isAudioFile) {
    // Direct processing
    downloadFromS3()
    generatePeaksWithFFmpeg()
    uploadPeaksToS3()
    updateDB('COMPLETE')
    return
} else if (isVideoFile) {
    // MediaConvert processing
    createMediaConvertJob()
    updateDB('PROCESSING')
    return
}
```

### Handler 2: handleCompletionHandler
```javascript
// Only for video files
if (jobStatus === 'COMPLETE') {
    updateDB('COMPLETE')
} else if (jobStatus === 'ERROR') {
    updateDB('FAILED')
}
```

## 🚀 Benefits

### Performance
- **Audio files**: 6x faster (5s vs 30s)
- **Video files**: Same performance
- **Storage**: 33% less (no MP3 intermediate)

### Complexity
- **Before**: 3 handlers, 3 triggers
- **After**: 2 handlers, 2 triggers
- **Code**: 40% less code

### Reliability
- **Fewer moving parts**: Less chance of failure
- **Simpler debugging**: Clearer flow
- **Better error handling**: Direct processing

## 📦 AWS Resources

### Required
- **S3 Input Bucket**: Trigger `startConversionHandler`
- **S3 Output Bucket**: Store peaks and HLS files
- **EventBridge**: Trigger `handleCompletionHandler` for video
- **Lambda**: 2 functions instead of 3
- **MediaConvert**: Only for video files
- **RDS**: Database for job tracking

### Optional
- **CloudWatch**: Logging and monitoring
- **IAM**: Permissions for all services

## 🔄 Migration Path

### Step 1: Update Lambda Configuration
```bash
# Remove old trigger
aws lambda remove-permission \
  --function-name generatePeaksHandler \
  --statement-id s3-trigger

# Keep existing triggers
# - S3 Input Bucket → startConversionHandler
# - EventBridge → handleCompletionHandler
```

### Step 2: Update Frontend
```javascript
// Change peaks file path
const peaksPath = filePath.replace('.mp3', '_peaks.txt')
                         .replace('-converted_peaks.txt', '_peaks.txt');
```

### Step 3: Monitor
- Check CloudWatch logs
- Verify database updates
- Test audio and video flows

## 🧪 Testing

### Audio File Test
```bash
# Upload audio file
aws s3 cp audio.wav s3://input-bucket/test/audio.wav

# Check logs
aws logs tail /aws/lambda/startConversionHandler

# Verify peaks file
aws s3 ls s3://output-bucket/test/audio_peaks.txt
```

### Video File Test
```bash
# Upload video file
aws s3 cp video.mp4 s3://input-bucket/test/video.mp4

# Check MediaConvert job
aws mediaconvert get-job --id <job-id>

# Verify HLS files
aws s3 ls s3://output-bucket/test/video-converted.m3u8
``` 