# Soremo MediaConvert Lambda

Lambda function for media conversion and waveform data generation for the Soremo platform.

## Key Features

- **Media Conversion**: Convert video files to HLS format using AWS MediaConvert
- **Waveform Generation**: Generate waveform data for audio files using audiowaveform
- **Database Integration**: Store job status and metadata in MySQL database
- **S3 Integration**: Automatically process files from S3 input bucket

## Architecture

### Handlers

1. **startConversionHandler**: Triggered by S3 events
   - Detect file type (audio/video)
   - Audio files: Generate waveform peaks directly
   - Video files: Create MediaConvert job

2. **handleCompletionHandler**: Triggered by MediaConvert completion events
   - Process MediaConvert results
   - Generate waveform peaks for audio track
   - Update database status

## Waveform Generation

The project uses [audiowaveform](https://github.com/bbc/audiowaveform) for waveform data generation:

- **High performance**: Optimized for waveform generation
- **Multiple format support**: MP3, WAV, FLAC, OGG, M4A, AAC
- **JSON output**: Compatible with wavesurfer.js

### Output Format

Waveform data is saved as JSON:

```json
{
  "version": 2,
  "channels": 1,
  "sample_rate": 44100,
  "samples_per_pixel": 512,
  "bits": 8,
  "length": 64,
  "data": [min1, max1, min2, max2, ...]
}
```

### Frontend Usage

```javascript
// Load JSON peaks data (recommended)
fetch('/path/to/file_peaks.json')
  .then(response => response.json())
  .then(peaksData => {
    wavesurfer.backend.setMergedPeaks(peaksData.data);
  });

// Or access directly from wavesurfer
let peaks = wavesurfer.backend.mergedPeaks;
if (peaks) {
    console.log(`Peaks: ${peaks.length} values`);
}
```

## Deployment

### Prerequisites

- Docker
- AWS CLI configured
- ECR repository created

### Build and Deploy

```bash
# Build Docker image
./deploy.sh

# Build and push to ECR
./deploy.sh push
```

### Environment Variables

```bash
OUTPUT_BUCKET=your-output-bucket
MEDIA_CONVERT_ROLE_ARN=your-mediaconvert-role-arn
MEDIA_CONVERT_ENDPOINT=your-mediaconvert-endpoint
RDS_PARAM_NAME=your-database-parameter-name
```

### Lambda Configuration

- **Runtime**: Container image
- **Memory**: 3008 MB (recommended)
- **Timeout**: 15 minutes
- **Architecture**: x86_64

### Triggers

1. **S3 Event**: Configure input bucket to trigger `startConversionHandler`
2. **EventBridge**: Configure MediaConvert completion events to trigger `handleCompletionHandler`

## Database Schema

```sql
CREATE TABLE app_mediaconvertjob (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_object_key VARCHAR(255) NOT NULL UNIQUE,
    status ENUM('SUBMITTED', 'PROGRESSING', 'COMPLETE', 'FAILED') NOT NULL,
    job_id VARCHAR(255),
    output_object_key VARCHAR(255),
    waveform_data_key VARCHAR(255),
    error_message TEXT,
    created DATETIME NOT NULL,
    modified DATETIME NOT NULL
);
```

## Testing

```bash
# Test audiowaveform integration
node test-audiowaveform.js
```

## Supported Formats

### Audio Files (Direct Processing)
- MP3, WAV, M4A, AAC, FLAC, OGG

### Video Files (MediaConvert)
- MP4, MOV, MKV, AVI, WebM, MPG

## Architecture Benefits

- **Optimized for Audio**: Direct processing for audio files
- **Scalable**: Uses AWS managed services
- **Cost-effective**: Only uses MediaConvert for video files
- **Fast**: audiowaveform is faster than FFmpeg for waveform generation
