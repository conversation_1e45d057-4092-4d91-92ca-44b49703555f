# Soremo MediaConvert Lambda (Node.js)

Lambda function để convert media files và tạo waveform data, được viết hoàn toàn bằng Node.js với sự tương thích 100% với wavesurfer.js.

## ✨ Tính năng chính

- **Direct Audio Processing**: Xử lý audio files trực tiếp, generate peaks ngay lập tức
- **Video MediaConvert**: Chuyển đổi video sang HLS format (vẫn dùng MediaConvert)
- **Wavesurfer.js Compatible**: Tạo peaks data tương thích 100% với wavesurfer.js
- **Database Integration**: MySQL/RDS integration với AWS SSM Parameter Store
- **Multi-format Support**: MP3, WAV, MP4, MOV, và nhiều format khác
- **Pure Wavesurfer**: Chỉ tạo wavesurfer peaks (không cần audiowaveform nữa)

## 🏗️ Architecture

Gồm 2 Lambda handlers:

1. **startConversionHandler**: Trigger bởi S3 upload → Xử lý audio trực tiếp hoặc tạo MediaConvert job cho video
2. **handleCompletionHandler**: Trigger bởi EventBridge → Xử lý kết quả video conversion

> **Note**: Handler 3 đã được loại bỏ vì audio files được xử lý trực tiếp trong handler 1

## 📦 Dependencies

- `aws-sdk`: AWS services integration
- `mysql2`: Database connectivity
- `fluent-ffmpeg`: Audio processing và waveform generation

## 🚀 Deployment

### 1. Build Docker image

```bash
./deploy.sh
```

### 2. Push to ECR (optional)

```bash
./deploy.sh push
```

### 3. Environment Variables

Set trong Lambda configuration:

```
OUTPUT_BUCKET=your-output-bucket
MEDIA_CONVERT_ROLE_ARN=arn:aws:iam::account:role/MediaConvertRole  
MEDIA_CONVERT_ENDPOINT=https://your-endpoint.mediaconvert.region.amazonaws.com
RDS_PARAM_NAME=/soremo/db-credentials
```

### 4. Database Parameter

Tạo SSM Parameter với format JSON:

```json
{
  "db_host": "your-rds-endpoint",
  "db_port": 3306,
  "db_username": "username", 
  "db_password": "password",
  "db_name": "database_name"
}
```

### 5. Configure Triggers

- **S3 Input Bucket** → `startConversionHandler`
- **EventBridge MediaConvert** → `handleCompletionHandler` 

> **Note**: Không cần trigger S3 Output Bucket nữa vì audio files được xử lý trực tiếp

## 📊 Output Files

### Audio Files → Direct Processing

- `folder/file_peaks.txt`: Wavesurfer-compatible peaks (32 values) - **Không cần convert sang MP3**

### Video Files → HLS

- `folder/file-converted.m3u8`: HLS playlist
- `folder/file-converted000.ts`: Video segments

## 🎵 Wavesurfer Integration

MergedPeaks data được tạo **chính xác theo logic wavesurfer.js**:

```javascript
// Frontend sử dụng JSON peaks data (recommended)
fetch('/path/to/file_peaks.json')
  .then(response => response.json())
  .then(peaksData => {
    // peaksData.data contains the mergedPeaks array
    wavesurfer.backend.setMergedPeaks(peaksData.data);
  });

// Hoặc truy cập trực tiếp từ wavesurfer
let peaks = wavesurfer.backend.mergedPeaks;
if (peaks) {
    console.log(`Peaks: ${peaks.length} values`);
}
```

### MergedPeaks vs Peaks
- **Peaks**: Chỉ max values `[0.5, 0.8, 0.3, ...]`
- **MergedPeaks**: Min-max pairs `[-0.3, 0.5, -0.2, 0.8, ...]`
- **Benefits**: Waveform chi tiết hơn, hiển thị cả positive và negative amplitudes

### Audio Processing
- **Direct processing**: Xử lý trực tiếp từ file gốc (MP3, WAV, M4A, AAC, FLAC, OGG)
- **WAV conversion**: Convert sang WAV tạm thời để xử lý
- **FFmpeg WAV**: Sử dụng FFmpeg để convert và extract audio samples
- **Error handling**: Xử lý lỗi FFmpeg pipe trong Lambda environment

## 🔧 Development

### Local Testing

```bash
# Install dependencies
npm install

# Test individual functions
node -e "
const { generatePeaksHandler } = require('./index.js');
// Test with mock event
"
```

### Debug Logs

Lambda logs sẽ hiển thị chi tiết:

```
[INFO] Generating wavesurfer-compatible peaks data...
[INFO] Audio loaded - Samples: 2205000
[SUCCESS] Wavesurfer peaks generated successfully
```

## 📋 Database Schema

Lambda expects table `app_mediaconvertjob`:

```sql
CREATE TABLE app_mediaconvertjob (
  id INT AUTO_INCREMENT PRIMARY KEY,
  original_object_key VARCHAR(500),
  converted_media_key VARCHAR(500),
  waveform_data_key VARCHAR(500),
  status ENUM('PROCESSING', 'GENERATING_PEAKS', 'COMPLETE', 'FAILED'),
  job_id VARCHAR(100),
  error_message TEXT,
  created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🛠️ Troubleshooting

### Common Issues

1. **FFmpeg not found**: Ensure ffmpeg trong Docker image
2. **Database connection**: Check SSM parameter format
3. **Memory issues**: Increase Lambda memory cho large files
4. **Audio conversion errors**: Check supported formats

### Performance Tips

- **Timeout**: Set Lambda timeout ≥ 5 minutes cho large files
- **Memory**: Recommend 1GB+ cho audio processing
- **Layers**: Consider ffmpeg Layer để giảm image size

## 📈 Monitoring

CloudWatch metrics để monitor:

- Duration: Processing time per file
- Errors: Failed conversions
- Memory: Peak memory usage

## 🔄 Migration từ Python

Đã migrate toàn bộ từ Python:

- ✅ **No more `pyaudioop` errors**
- ✅ **100% wavesurfer.js compatibility** 
- ✅ **Simpler architecture** - chỉ cần ffmpeg
- ✅ **Lighter dependencies** - bỏ audiowaveform
- ✅ **Faster processing** - ít tools hơn

---

**🎯 Result**: MergedPeaks data được tạo **giống hệt** với `wavesurfer.backend.mergedPeaks` từ file gốc! 